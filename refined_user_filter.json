{
  "solution": "Consolidated User State Management",
  "approach": "Replace multiple conditional branches with a single user state handler",
  
  "main_changes": {
    "1": "Replace Main Switch with Unified User Handler",
    "2": "Consolidate user roles into single classification",
    "3": "Use single callback router for all interactions",
    "4": "Eliminate redundant user checks"
  },

  "new_main_switch_rules": {
    "rules": [
      {
        "value2": "/start",
        "output": 0,
        "description": "Handle start command and new members"
      },
      {
        "value2": "callback",
        "output": 1,
        "description": "All callback queries (buttons, menus)"
      },
      {
        "value2": "admin_command",
        "output": 2,
        "description": "Admin commands (/kick, /pin, view)"
      },
      {
        "value2": "fallback",
        "output": 3,
        "description": "Unhandled inputs"
      }
    ]
  },

  "unified_user_state_handler": {
    "jsCode": `// Unified User State Handler - Replaces multiple user checks
const user = $json.user || $json.supabaseResults?.[0];
const isCallback = !!$json.callback_query;
const isNewMember = !!$json.chat_member;
const isStart = $json.message?.text?.startsWith('/start');

// Determine user state in single place
let userState = 'guest';
let userRole = 'guest';

if (user) {
  userRole = user.role || 'member';
  switch(userRole) {
    case 'admin':
    case 'superuser':
      userState = 'admin';
      break;
    case 'investor':
      userState = 'investor';
      break;
    case 'member':
      userState = 'member';
      break;
    default:
      userState = 'guest';
  }
} else {
  // Create guest user for consistent handling
  user = {
    telegram_id: $json.unifiedUserId,
    username: $json.unifiedUsername || '',
    name: $json.unifiedFirstName || 'Guest',
    role: 'guest',
    is_verified_investor: false
  };
}

// Route based on interaction type, not user state
let routeType = 'menu';
if (isStart || isNewMember) {
  routeType = 'welcome';
} else if (isCallback) {
  routeType = 'callback';
} else if ($json.message?.text?.startsWith('/')) {
  routeType = 'command';
}

return {
  json: {
    ...$json,
    user: user,
    userState: userState,
    userRole: userRole,
    routeType: routeType,
    isGuest: userState === 'guest'
  }
};`
  },

  "callback_router_consolidation": {
    "description": "Single router handles all callbacks with user context",
    "switch_cases": [
      "main_menu",
      "services_menu", 
      "donate",
      "register_user",
      "view_investment",
      "show_roadmap_public",
      "All guest_* callbacks",
      "All registration callbacks",
      "All order callbacks"
    ]
  },

  "menu_builder_enhancement": {
    "jsCode": `// Enhanced Menu Builder - Adapts to any user state
const { user, userState, isGuest } = $json;

// Dynamic menu based on user state
const menus = {
  guest: {
    welcome: "👋 Welcome to ELOH Processing DAO\\n\\n🔓 Browsing as Guest",
    buttons: [
      [{ text: "🗺️ View Roadmap", callback_data: "show_roadmap_public" }],
      [{ text: "💰 Investment Info", callback_data: "guest_investment_info" }],
      [{ text: "✅ Create Account", callback_data: "register_user" }]
    ]
  },
  member: {
    welcome: "🚀 Welcome back, " + (user?.name || 'Member') + "!",
    buttons: [
      [{ text: "🗺️ Roadmap", callback_data: "show_roadmap_public" }],
      [{ text: "🔧 Services", callback_data: "services_menu" }],
      [{ text: "💝 Donate", callback_data: "donate" }]
    ]
  },
  investor: {
    welcome: "💎 Welcome, Investor " + (user?.name || 'User') + "!",
    buttons: [
      [{ text: "💰 My Investment", callback_data: "view_investment" }],
      [{ text: "🔧 Services", callback_data: "services_menu" }],
      [{ text: "🗺️ Roadmap", callback_data: "show_roadmap_public" }]
    ]
  },
  admin: {
    welcome: "🛡️ Admin Panel - " + (user?.name || 'Admin'),
    buttons: [
      [{ text: "👥 Manage Users", callback_data: "admin_users" }],
      [{ text: "📊 Analytics", callback_data: "admin_analytics" }],
      [{ text: "🔧 Services", callback_data: "services_menu" }]
    ]
  }
};

const currentMenu = menus[userState] || menus.guest;

return {
  chatId: $json.unifiedChatId,
  text: currentMenu.welcome,
  reply_markup: {
    inline_keyboard: currentMenu.buttons
  },
  parse_mode: "Markdown"
};`
  },

  "implementation_steps": [
    "1. Replace Main Switch node with simplified 4-output version",
    "2. Add Unified User State Handler after user lookup",
    "3. Update Callback Router to handle all callback types",
    "4. Remove redundant user check nodes (If role, If ADMIN, etc.)",
    "5. Update menu builders to use unified user state",
    "6. Test all user flows with new structure"
  ],

  "benefits": [
    "Eliminates output index errors",
    "Reduces complexity from ~50 nodes to ~30 nodes", 
    "Single source of truth for user state",
    "Easier to maintain and debug",
    "Consistent user experience across all states",
    "Better error handling"
  ]
}