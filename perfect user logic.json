{
  "name": "My workflow copy 2",
  "nodes": [
    {
      "parameters": {
        "color": "#FF9900"
      },
      "id": "7fd11cd6-b65c-4ed1-808d-a8d9f652956a",
      "name": "‼️ READ FIRST: Setup Instructions",
      "type": "n8n-nodes-base.stickyNote",
      "typeVersion": 1,
      "position": [
        -2432,
        -880
      ]
    },
    {
      "parameters": {
        "updates": [
          "message",
          "callback_query",
          "chat_member",
          "pre_checkout_query",
          "successful_payment"
        ],
        "additionalFields": {}
      },
      "id": "ded05082-dec0-4b7f-9b1a-d79ebf6e73a8",
      "name": "Telegram Trigger",
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.1,
      "position": [
        -2416,
        -640
      ],
      "webhookId": "4f5004d1-a570-4de8-a614-79b61c34a9e9",
      "credentials": {
        "telegramApi": {
          "id": "NZTubkhiO2CC30p0",
          "name": "Telegram Payment Provider"
        }
      }
    },
{
"callbackHandler": {
      "main": [
        [
          {
            "node": "ifRedirectToMenu",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "ifRedirectToMenu": {
      "main": [
        [
          {
            "node": "dynamicMenuBuilder",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "answerCallbackQuery",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "answerCallbackQuery": {
      "main": [
        [
          {
            "node": "sendCallbackResponse",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
  "name": "Telegram Bot - v6 FINAL (IMPORTABLE)",
  "nodes": [
    {
      "parameters": {
        "text": "**SETUP INSTRUCTIONS:**\\n\\n1.  **Database Index:** For this workflow to perform well, you MUST create an index on the `telegram_id` column in your `users` table. \\n    `CREATE INDEX idx_users_telegram_id ON public.users (telegram_id);`\\n\\n2.  **Credentials:** Use n8n's built-in credential manager for:\\n    - Telegram Bot API\\n    - PostgreSQL/Supabase Connection\\n    - Payment Provider Token\\n\\n3.  **Admin Chat ID:** Set your personal or admin group chat ID in the `Notify Admin of Error` node.",
        "color": "#FF9900"
      },
      "id": "setupInstructions",
      "name": "‼️ READ FIRST: Setup Instructions",
      "type": "n8n-nodes-base.stickyNote",
      "typeVersion": 1,
      "position": [
        0,
        50
      ]
    },
    {
      "parameters": {
        "updates": [
          "message",
          "callback_query",
          "chat_member",
          "pre_checkout_query",
          "successful_payment"
        ]
      },
      "id": "telegramTrigger",
      "name": "Telegram Trigger",
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.1,
      "position": [
        20,
        300
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM public.users WHERE telegram_id = $1 LIMIT 1;",
        "values": "={{ [$json.unifiedUserId] }}",
        "options": {
          "alwaysOutputData": true
        }
      },
      "id": "lookupUser",
      "name": "1. Lookup User (SECURE)",
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 2.4,
      "position": [
        460,
        300
      ],
      "credentials": {
        "supabaseApi": {
          "id": "supabase_credentials",
          "name": "Supabase Connection"
        }
      },
      "onError": "continue"
    },
    {
      "parameters": {
        "jsCode": "// --- Unified User State Handler ---\n\nconst message = $json.message || $json.callback_query?.message;\nconst callbackData = $json.callback_query?.data;\nconst text = $json.message?.text;\nconst isNewMember = !!$json.chat_member;\nconst userRecord = $items('Lookup User')[0].json;\nlet user = userRecord;\nlet userState = 'guest';\n\nif (user) {\n  switch(user.role) {\n    case 'admin': case 'superuser': userState = 'admin'; break;\n    case 'investor': userState = 'investor'; break;\n    case 'member': default: userState = 'member'; break;\n  }\n} else {\n  const from = $json.message?.from || $json.callback_query?.from || $json.chat_member?.from || $json.pre_checkout_query?.from || $json.successful_payment?.from;\n  user = {\n    telegram_id: from.id,\n    username: from.username || '',\n    name: from.first_name || 'Guest',\n    role: 'guest',\n    is_verified_investor: false\n  };\n}\n\nlet interactionType = 'unhandled';\nif (text?.startsWith('/start') || isNewMember) interactionType = 'welcome';\nelse if (callbackData) interactionType = 'callback';\nelse if (text?.startsWith('/')) interactionType = 'admin_command';\nelse if ($json.pre_checkout_query) interactionType = 'pre_checkout';\nelse if ($json.successful_payment) interactionType = 'successful_payment';\n\nreturn {\n  ...$json,\n  user,\n  userState,\n  interactionType,\n  callbackData\n};"
      },
      "id": "unifiedUserState",
      "name": "3. Unified User State Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        880,
        300
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {},
          "conditions": [
            {
              "leftValue": "={{ $item('error').isDefined() }}",
              "operation": {
                "type": "boolean",
                "operation": "true"
              }
            }
          ]
        },
        "options": {}
      },
      "id": "ifDbError",
      "name": "If DB Error?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        680,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// --- Smart Interaction Router ---\nconst { interactionType } = $json;\n\nswitch(interactionType) {\n  case 'welcome': return { output: 0 };\n  case 'callback': return { output: 1 };\n  case 'admin_command': return { output: 2 };\n  case 'pre_checkout': return { output: 3 };\n  case 'successful_payment': return { output: 4 };\n  default: return { output: 5 };\n}"
      },
      "id": "smartInteractionRouter",
      "name": "4. Smart Interaction Router",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1100,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// --- Dynamic Menu Builder ---\nconst { user, userState, unifiedChatId } = $json;\nconst menus = {\n  guest: {\n    welcome: '👋 Welcome to ELOH Processing DAO\\n\\n🔓 You are browsing as a Guest.',\n    buttons: [[{ text: '🗺️ View Roadmap', callback_data: 'show_roadmap_public' }], [{ text: '💰 Investment Info', callback_data: 'guest_investment_info' }], [{ text: '✅ Create Account', callback_data: 'register_user' }]]\n  },\n  member: {\n    welcome: '🚀 Welcome back, ' + (user.name || 'Member') + '!',\n    buttons: [[{ text: '🗺️ Roadmap', callback_data: 'show_roadmap_public' }], [{ text: '🔧 Our Services', callback_data: 'services_menu' }], [{ text: '💝 Make a Donation', callback_data: 'donate' }]]\n  },\n  investor: {\n    welcome: '💎 Welcome, Investor ' + (user.name || 'User') + '!',\n    buttons: [[{ text: '💰 My Investment', callback_data: 'view_investment' }], [{ text: '🔧 Our Services', callback_data: 'services_menu' }], [{ text: '🗺️ Roadmap', callback_data: 'show_roadmap_public' }]]\n  },\n  admin: {\n    welcome: '🛡️ Admin Panel - Welcome ' + (user.name || 'Admin'),\n    buttons: [[{ text: '👥 Manage Users', callback_data: 'admin_users' }], [{ text: '📊 View Analytics', callback_data: 'admin_analytics' }], [{ text: '🔧 Our Services', callback_data: 'services_menu' }]]\n  }\n};\nconst currentMenu = menus[userState] || menus.guest;\nreturn {\n  chatId: unifiedChatId,\n  text: currentMenu.welcome,\n  reply_markup: {\n    inline_keyboard: currentMenu.buttons\n  },\n  parse_mode: 'Markdown'\n};"
      },
      "id": "dynamicMenuBuilder",
      "name": "Dynamic Menu Builder",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1320,
        -100
      ]
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "={{ $json.text }}",
        "replyMarkup": "={{ $json.reply_markup }}",
        "parseMode": "={{ $json.parse_mode }}"
      },
      "id": "sendMenu",
      "name": "Send Menu",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1540,
        -100
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "chatId": "YOUR_ADMIN_CHAT_ID",
        "text": "🚨 **Workflow Error** 🚨\n\nAn error occurred in the workflow.\n\n**Node**: `{{$node.name}}`\n**Error**: `{{$json.error.message}}`"
      },
      "id": "notifyAdminOfError",
      "name": "Notify Admin of Error",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        680,
        500
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "Sorry, a technical error occurred. The administrators have been notified. Please try again later."
      },
      "id": "sendUserErrorMsg",
      "name": "Send User Error Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        900,
        500
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO public.user_deposits (user_id, amount, currency, telegram_payment_id, deposit_type, status) SELECT user_id, $2, $3, $4, 'investment', 'completed' FROM public.users WHERE telegram_id = $1::text",
        "values": "={{ [$json.successful_payment.from.id, $json.successful_payment.total_amount / 100, $json.successful_payment.currency, $json.successful_payment.telegram_payment_charge_id] }}"
      },
      "id": "recordPayment",
      "name": "4c. Record Payment (SECURE)",
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 1,
      "position": [
        1540,
        900
      ],
      "credentials": {
        "postgres": {
          "id": "supabase_credentials",
          "name": "Supabase Connection"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT id FROM public.user_deposits WHERE telegram_payment_id = $1 LIMIT 1;",
        "values": "={{ [$json.successful_payment.telegram_payment_charge_id] }}"
      },
      "id": "checkForDuplicatePayment",
      "name": "4a. Check for Duplicate Payment",
      "type": "n8n-nodes-base.supabase",
      "typeVersion": 1,
      "position": [
        1320,
        700
      ],
      "onError": "continue"
    },
    {
      "parameters": {
        "conditions": {
          "options": {},
          "conditions": [
            {
              "leftValue": "={{ $items('checkForDuplicatePayment')[0].json.id }}",
              "operation": {
                "type": "generic",
                "operation": "exists"
              }
            }
          ]
        },
        "options": {}
      },
      "id": "ifDuplicatePayment",
      "name": "If Duplicate Payment",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        1540,
        700
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {},
          "conditions": [
            {
              "leftValue": "={{ $item('error').isDefined() }}",
              "operation": {
                "type": "boolean",
                "operation": "true"
              }
            }
          ]
        },
        "options": {}
      },
      "id": "ifDuplicateCheckError",
      "name": "If Duplicate Check Error?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        1320,
        900
      ]
    },
    {
      "parameters": {
        "operation": "answerPreCheckoutQuery",
        "preCheckoutQueryId": "={{ $json.pre_checkout_query.id }}",
        "ok": true
      },
      "id": "answerPreCheckoutQuery",
      "name": "Answer Pre-Checkout Query",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1320,
        500
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "✅ Payment of ${{ $json.successful_payment.total_amount / 100 }} {{ $json.successful_payment.currency }} received! Thank you for your investment."
      },
      "id": "sendPaymentConfirmation",
      "name": "4d. Send Payment Confirmation",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1760,
        900
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// Reliably get User and Chat ID from any trigger\n\nconst from = $json.message?.from || $json.callback_query?.from || $json.chat_member?.from || $json.pre_checkout_query?.from || $json.successful_payment?.from;\nconst chat = $json.message?.chat || $json.callback_query?.message?.chat;\n\n$json.unifiedUserId = from.id;\n$json.unifiedChatId = chat?.id || from.id; // Chat ID for groups, User ID for private\n\nreturn $json;"
      },
      "id": "prepareIDs",
      "name": "0. Prepare IDs",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        240,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// --- Callback Handler ---\nconst { callbackData, user, userState, unifiedChatId } = $json;\n\n// Define callback responses based on user state and callback data\nconst responses = {\n  // Public/Guest responses\n  'show_roadmap_public': {\n    text: '🗺️ **ELOH Processing DAO Roadmap**\\n\\nHere\\'s our development roadmap:\\n\\n✅ Phase 1: Foundation\\n🔄 Phase 2: Core Development\\n⏳ Phase 3: Beta Launch\\n⏳ Phase 4: Full Launch',\n    parse_mode: 'Markdown'\n  },\n  'guest_investment_info': {\n    text: '💰 **Investment Information**\\n\\nMinimum Investment: $100\\nExpected Returns: Variable\\nRisk Level: High\\n\\n⚠️ This is a high-risk investment. Please do your own research.',\n    parse_mode: 'Markdown',\n    reply_markup: {\n      inline_keyboard: [[\n        { text: '✅ Create Account to Invest', callback_data: 'register_user' }\n      ]]\n    }\n  },\n  'register_user': {\n    text: '✅ **Account Creation**\\n\\nTo create an account, please contact our admin team. We\\'ll verify your identity and set up your investor account.',\n    parse_mode: 'Markdown'\n  },\n  \n  // Member responses\n  'services_menu': {\n    text: '🔧 **Our Services**\\n\\nChoose a service:',\n    reply_markup: {\n      inline_keyboard: [\n        [{ text: '💻 Development Services', callback_data: 'dev_services' }],\n        [{ text: '🎨 Design Services', callback_data: 'design_services' }],\n        [{ text: '📊 Consulting', callback_data: 'consulting' }],\n        [{ text: '🔙 Back to Main Menu', callback_data: 'main_menu' }]\n      ]\n    }\n  },\n  'donate': {\n    text: '💝 **Make a Donation**\\n\\nThank you for supporting ELOH Processing DAO!\\n\\nChoose donation amount:',\n    reply_markup: {\n      inline_keyboard: [\n        [{ text: '$10', callback_data: 'donate_10' }, { text: '$25', callback_data: 'donate_25' }],\n        [{ text: '$50', callback_data: 'donate_50' }, { text: '$100', callback_data: 'donate_100' }],\n        [{ text: '🔙 Back', callback_data: 'main_menu' }]\n      ]\n    }\n  },\n  \n  // Investor responses\n  'view_investment': {\n    text: '💎 **Your Investment Portfolio**\\n\\nTotal Invested: $0.00\\nCurrent Value: $0.00\\nROI: 0%\\n\\n📈 Performance charts coming soon!',\n    parse_mode: 'Markdown'\n  },\n  \n  // Admin responses\n  'admin_users': {\n    text: '👥 **User Management**\\n\\nAdmin panel for user management coming soon!',\n    reply_markup: {\n      inline_keyboard: [[\n        { text: '🔙 Back to Admin Panel', callback_data: 'main_menu' }\n      ]]\n    }\n  },\n  'admin_analytics': {\n    text: '📊 **Analytics Dashboard**\\n\\nAnalytics panel coming soon!',\n    reply_markup: {\n      inline_keyboard: [[\n        { text: '🔙 Back to Admin Panel', callback_data: 'main_menu' }\n      ]]\n    }\n  },\n  \n  // Service details\n  'dev_services': {\n    text: '💻 **Development Services**\\n\\n• Web Development\\n• Mobile Apps\\n• Smart Contracts\\n• API Integration\\n\\nContact us for pricing!',\n    reply_markup: {\n      inline_keyboard: [[\n        { text: '🔙 Back to Services', callback_data: 'services_menu' }\n      ]]\n    }\n  },\n  'design_services': {\n    text: '🎨 **Design Services**\\n\\n• UI/UX Design\\n• Branding\\n• Graphics\\n• Illustrations\\n\\nContact us for pricing!',\n    reply_markup: {\n      inline_keyboard: [[\n        { text: '🔙 Back to Services', callback_data: 'services_menu' }\n      ]]\n    }\n  },\n  'consulting': {\n    text: '📊 **Consulting Services**\\n\\n• Business Strategy\\n• Technical Consulting\\n• Project Management\\n• Market Analysis\\n\\nContact us for pricing!',\n    reply_markup: {\n      inline_keyboard: [[\n        { text: '🔙 Back to Services', callback_data: 'services_menu' }\n      ]]\n    }\n  },\n  \n  // Main menu redirect\n  'main_menu': {\n    redirect: 'main_menu'\n  }\n};\n\n// Get response for callback data\nconst response = responses[callbackData];\n\nif (!response) {\n  return {\n    chatId: unifiedChatId,\n    text: '❌ Unknown command. Please try again.',\n    showAlert: false\n  };\n}\n\n// Handle redirect to main menu\nif (response.redirect === 'main_menu') {\n  return {\n    ...$json,\n    redirectToMenu: true\n  };\n}\n\n// Answer callback query and send message\nreturn {\n  chatId: unifiedChatId,\n  text: response.text,\n  reply_markup: response.reply_markup,\n  parse_mode: response.parse_mode || 'HTML',\n  showAlert: false\n};"
      },
      "id": "callbackHandler",
      "name": "Callback Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1320,
        100
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {},
          "conditions": [
            {
              "leftValue": "={{ $json.redirectToMenu }}",
              "operation": {
                "type": "boolean",
                "operation": "true"
              }
            }
          ]
        },
        "options": {}
      },
      "id": "ifRedirectToMenu",
      "name": "If Redirect to Menu?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        1540,
        100
      ]
    },
    {
      "parameters": {
        "operation": "answerCallbackQuery",
        "callbackQueryId": "={{ $json.callback_query.id }}",
        "text": "",
        "showAlert": false
      },
      "id": "answerCallbackQuery",
      "name": "Answer Callback Query",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1760,
        0
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.chatId }}",
        "text": "={{ $json.text }}",
        "replyMarkup": "={{ $json.reply_markup }}",
        "parseMode": "={{ $json.parse_mode }}"
      },
      "id": "sendCallbackResponse",
      "name": "Send Callback Response",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1760,
        200
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "executeOnce": false,
        "options": {}
      },
      "id": "adminCommandPlaceholder",
      "name": "TODO: Admin Command Handler",
      "type": "n8n-nodes-base.noOp",
      "typeVersion": 1,
      "position": [
        1320,
        300
      ]
    },
    {
      "parameters": {
        "executeOnce": false,
        "options": {}
      },
      "id": "unhandledPlaceholder",
      "name": "NOTE: Unhandled Interaction",
      "type": "n8n-nodes-base.noOp",
      "typeVersion": 1,
      "position": [
        1320,
        1100
      ]
    },
    {
      "parameters": {
        "executeOnce": false,
        "options": {}
      },
      "id": "duplicateIgnored",
      "name": "NOTE: Duplicate Payment Ignored",
      "type": "n8n-nodes-base.noOp",
      "typeVersion": 1,
      "position": [
        1760,
        700
      ]
    }
  ],
  "connections": {
    "telegramTrigger": {
      "main": [
        [
          {
            "node": "prepareIDs",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "prepareIDs": {
      "main": [
        [
          {
            "node": "lookupUser",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "lookupUser": {
      "main": [
        [
          {
            "node": "ifDbError",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "ifDbError": {
      "main": [
        [
          {
            "node": "notifyAdminOfError",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "unifiedUserState",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "unifiedUserState": {
      "main": [
        [
          {
            "node": "smartInteractionRouter",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "smartInteractionRouter": {
      "main": [
        [
          {
            "node": "dynamicMenuBuilder",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "callbackHandler",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "adminCommandPlaceholder",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "answerPreCheckoutQuery",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "checkForDuplicatePayment",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "unhandledPlaceholder",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "dynamicMenuBuilder": {
      "main": [
        [
          {
            "node": "sendMenu",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "notifyAdminOfError": {
      "main": [
        [
          {
            "node": "sendUserErrorMsg",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "checkForDuplicatePayment": {
      "main": [
        [
          {
            "node": "ifDuplicateCheckError",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "ifDuplicateCheckError": {
      "main": [
        [
          {
            "node": "notifyAdminOfError",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "ifDuplicatePayment",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "ifDuplicatePayment": {
      "main": [
        [
          {
            "node": "duplicateIgnored",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "recordPayment",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "recordPayment": {
      "main": [
        [
          {
            "node": "sendPaymentConfirmation",
            "type": "main",
            "index": 0
          }
          ]
        ]
      },
    "answerPreCheckoutQuery": {
      "main": [
        [
          {
            "node": "preCheckoutQueryAnswered",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "ifRedirectToMenu": {
      "main": [
        [
          {
            "node": "dynamicMenuBuilder",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "answerCallbackQuery",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "answerCallbackQuery": {
      "main": [
        [
          {
            "node": "sendCallbackResponse",
        [[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[[zz    "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }   
}