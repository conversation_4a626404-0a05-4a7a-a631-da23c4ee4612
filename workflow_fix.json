{"name": "My workflow copy 2", "nodes": [{"parameters": {"color": "#FF9900"}, "id": "7fd11cd6-b65c-4ed1-808d-a8d9f652956a", "name": "‼️ READ FIRST: Setup Instructions", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2432, -880]}, {"parameters": {"updates": ["message", "callback_query", "chat_member", "pre_checkout_query", "successful_payment"], "additionalFields": {}}, "id": "ded05082-dec0-4b7f-9b1a-d79ebf6e73a8", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-2416, -640], "webhookId": "4f5004d1-a570-4de8-a614-79b61c34a9e9", "credentials": {"telegramApi": {"id": "NZTubkhiO2CC30p0", "name": "Telegram Payment Provider"}}}, {"parameters": {"jsCode": "// --- Unified User State Handler ---\n\nconst message = $json.message || $json.callback_query?.message;\nconst callbackData = $json.callback_query?.data;\nconst text = $json.message?.text;\nconst isNewMember = !!$json.chat_member;\nconst userRecord = $items('Lookup User')[0].json;\nlet user = userRecord;\nlet userState = 'guest';\n\nif (user) {\n  switch(user.role) {\n    case 'admin': case 'superuser': userState = 'admin'; break;\n    case 'investor': userState = 'investor'; break;\n    case 'member': default: userState = 'member'; break;\n  }\n} else {\n  const from = $json.message?.from || $json.callback_query?.from || $json.chat_member?.from || $json.pre_checkout_query?.from || $json.successful_payment?.from;\n  user = {\n    telegram_id: from.id,\n    username: from.username || '',\n    name: from.first_name || 'Guest',\n    role: 'guest',\n    is_verified_investor: false\n  };\n}\n\nlet interactionType = 'unhandled';\nif (text?.startsWith('/start') || isNewMember) interactionType = 'welcome';\nelse if (callbackData) interactionType = 'callback';\nelse if (text?.startsWith('/')) interactionType = 'admin_command';\nelse if ($json.pre_checkout_query) interactionType = 'pre_checkout';\nelse if ($json.successful_payment) interactionType = 'successful_payment';\n\nreturn {\n  ...$json,\n  user,\n  userState,\n  interactionType,\n  callbackData\n};"}, "id": "7017bd2d-8da6-4f58-8e96-8ed76a19e727", "name": "3. Unified User State Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1552, -640]}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $item('error').isDefined() }}", "operation": {"type": "boolean", "operation": "true"}}]}, "options": {}}, "id": "f9ecefb4-5652-485a-b12e-9c9f6f11cdd3", "name": "If DB Error?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1824, -672]}, {"parameters": {"jsCode": "// --- Smart Interaction Router ---\nconst { interactionType } = $json;\n\nswitch(interactionType) {\n  case 'welcome': return { output: 0 };\n  case 'callback': return { output: 1 };\n  case 'admin_command': return { output: 2 };\n  case 'pre_checkout': return { output: 3 };\n  case 'successful_payment': return { output: 4 };\n  default: return { output: 5 };\n}"}, "id": "8014d16e-0ee1-4fe0-9323-faaaca5e1bc4", "name": "4. Smart Interaction Router", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1344, -640]}, {"parameters": {"jsCode": "// --- Dynamic Menu Builder ---\nconst { user, userState, unifiedChatId } = $json;\nconst menus = {\n  guest: {\n    welcome: '👋 Welcome to ELOH Processing DAO\\n\\n🔓 You are browsing as a Guest.',\n    buttons: [[{ text: '🗺️ View Roadmap', callback_data: 'show_roadmap_public' }], [{ text: '💰 Investment Info', callback_data: 'guest_investment_info' }], [{ text: '✅ Create Account', callback_data: 'register_user' }]]\n  },\n  member: {\n    welcome: '🚀 Welcome back, ' + (user.name || 'Member') + '!',\n    buttons: [[{ text: '🗺️ Roadmap', callback_data: 'show_roadmap_public' }], [{ text: '🔧 Our Services', callback_data: 'services_menu' }], [{ text: '💝 Make a Donation', callback_data: 'donate' }]]\n  },\n  investor: {\n    welcome: '💎 Welcome, Investor ' + (user.name || 'User') + '!',\n    buttons: [[{ text: '💰 My Investment', callback_data: 'view_investment' }], [{ text: '🔧 Our Services', callback_data: 'services_menu' }], [{ text: '🗺️ Roadmap', callback_data: 'show_roadmap_public' }]]\n  },\n  admin: {\n    welcome: '🛡️ Admin Panel - Welcome ' + (user.name || 'Admin'),\n    buttons: [[{ text: '👥 Manage Users', callback_data: 'admin_users' }], [{ text: '📊 View Analytics', callback_data: 'admin_analytics' }], [{ text: '🔧 Our Services', callback_data: 'services_menu' }]]\n  }\n};\nconst currentMenu = menus[userState] || menus.guest;\nreturn {\n  chatId: unifiedChatId,\n  text: currentMenu.welcome,\n  reply_markup: {\n    inline_keyboard: currentMenu.buttons\n  },\n  parse_mode: 'Markdown'\n};"}, "id": "fdae4e91-aece-4b67-adda-afed67fc7aa3", "name": "Dynamic Menu Builder", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1120, -1040]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "forceReply": {}, "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "2341540f-b347-4a5a-ad06-359cc98f55db", "name": "Send Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-896, -1040], "webhookId": "860aa445-a4f9-460e-a214-4d7445730821"}, {"parameters": {"chatId": "YOUR_ADMIN_CHAT_ID", "text": "🚨 **Workflow Error** 🚨\n\nAn error occurred in the workflow.\n\n**Node**: `{{$node.name}}`\n**Error**: `{{$json.error.message}}`", "additionalFields": {}}, "id": "051bdfc9-ba77-4dbb-a206-3d98074a401f", "name": "Notify Admin of Error", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1760, -432], "webhookId": "01356f79-d3fe-4b4d-bd9e-6bdd13d7c5c3", "credentials": {"telegramApi": {"id": "NZTubkhiO2CC30p0", "name": "Telegram Payment Provider"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Sorry, a technical error occurred. The administrators have been notified. Please try again later.", "additionalFields": {}}, "id": "aefa6378-be46-44e1-bdd8-ba28c2fc91eb", "name": "Send User Error Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1536, -432], "webhookId": "961ac9a5-5d6c-473f-b6c4-b719bb7c234e", "credentials": {"telegramApi": {"id": "NZTubkhiO2CC30p0", "name": "Telegram Payment Provider"}}}, {"parameters": {"operation": "execute<PERSON>uery"}, "id": "fd81024b-52be-44bb-a185-7615772aeaa3", "name": "4c. Record Payment (SECURE)", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-896, -32]}, {"parameters": {"operation": "get", "tableId": "user_deposits", "filters": {"conditions": [{"keyName": "user_id"}]}}, "id": "f2cbd408-4e5a-4e84-90b7-fd937d7a6bce", "name": "4a. Check for Duplicate Payment", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1120, -240], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}, "onError": "continue"}, {"parameters": {"conditions": {"options": {}, "conditions": [{"leftValue": "={{ $items('checkForDuplicatePayment')[0].json.id }}", "operation": {"type": "generic", "operation": "exists"}}]}, "options": {}}, "id": "834c4eea-6041-4612-8e72-ae48ea8cffc1", "name": "If Duplicate Payment", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1440, -48]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $item('error').isDefined() }}", "operation": {"type": "boolean", "operation": "true"}, "id": "a8bf1266-ae93-4bab-a379-27244a06bc4f"}], "combinator": "and"}, "options": {}}, "id": "710df856-0e42-4362-a247-ccaa4034a411", "name": "If Duplicate Check Error?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1120, -32]}, {"parameters": {"operation": "editMessageText", "replyMarkup": "inlineKeyboard", "additionalFields": {}}, "id": "c81b70c4-5f4e-4116-ad9a-fafed341ea6e", "name": "Answer Pre-Checkout Query", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1120, -432], "webhookId": "953bca78-3e9a-456c-b4f8-4baafe86560f", "credentials": {"telegramApi": {"id": "NZTubkhiO2CC30p0", "name": "Telegram Payment Provider"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "✅ Payment of ${{ $json.successful_payment.total_amount / 100 }} {{ $json.successful_payment.currency }} received! Thank you for your investment.", "additionalFields": {}}, "id": "f0ab7f59-61a1-4e55-abc8-b6a6e312938c", "name": "4d. Send Payment Confirmation", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-672, -32], "webhookId": "29bbea15-3c56-490d-88bd-ddbd0b216d12"}, {"parameters": {"jsCode": "// Reliably get User and Chat ID from any trigger\n\nconst from = $json.message?.from || $json.callback_query?.from || $json.chat_member?.from || $json.pre_checkout_query?.from || $json.successful_payment?.from;\nconst chat = $json.message?.chat || $json.callback_query?.message?.chat;\n\n$json.unifiedUserId = from.id;\n$json.unifiedChatId = chat?.id || from.id; // Chat ID for groups, User ID for private\n\nreturn $json;"}, "id": "6d0d858c-cca6-40a8-885f-6d7f0fce947d", "name": "0. Prepare IDs", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2192, -640]}, {"parameters": {}, "id": "264281c6-1184-48d8-82d6-2a72b413ccaf", "name": "TODO: <PERSON><PERSON> Handler", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-1120, -832]}, {"parameters": {}, "id": "d58d32f3-003f-4f50-89b9-56da60341edf", "name": "TODO: Admin Command Handler", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-1120, -640]}, {"parameters": {}, "id": "924f36d1-93e0-4f61-a6b8-f5a8708309fd", "name": "NOTE: Unhandled Interaction", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-1120, 176]}, {"parameters": {}, "id": "d2a56db3-fc5f-4c6b-895f-8a0ca6913450", "name": "NOTE: Duplicate Payment Ignored", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-672, -240]}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1984, -768], "id": "d61a21ed-9b59-46e0-bc74-328d10b93c03", "name": "Get a row", "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "0. Prepare IDs", "type": "main", "index": 0}]]}, "0. Prepare IDs": {"main": [[{"node": "Get a row", "type": "main", "index": 0}]]}, "Get a row": {"main": [[{"node": "If DB Error?", "type": "main", "index": 0}]]}, "If DB Error?": {"main": [[{"node": "3. Unified User State Handler", "type": "main", "index": 0}], [{"node": "Notify Admin of Error", "type": "main", "index": 0}]]}, "Notify Admin of Error": {"main": [[{"node": "Send User Error Message", "type": "main", "index": 0}]]}, "3. Unified User State Handler": {"main": [[{"node": "4. Smart Interaction Router", "type": "main", "index": 0}]]}, "4. Smart Interaction Router": {"main": [[{"node": "Dynamic Menu Builder", "type": "main", "index": 0}]]}, "Dynamic Menu Builder": {"main": [[{"node": "Send Menu", "type": "main", "index": 0}]]}, "4a. Check for Duplicate Payment": {"main": [[{"node": "If Duplicate Payment", "type": "main", "index": 0}]]}, "Answer Pre-Checkout Query": {"main": [[{"node": "4a. Check for Duplicate Payment", "type": "main", "index": 0}]]}, "If Duplicate Payment": {"main": [[{"node": "If Duplicate Check Error?", "type": "main", "index": 0}]]}, "4c. Record Payment (SECURE)": {"main": [[{"node": "4d. Send Payment Confirmation", "type": "main", "index": 0}]]}, "If Duplicate Check Error?": {"main": [[{"node": "NOTE: Duplicate Payment Ignored", "type": "main", "index": 0}], [{"node": "4c. Record Payment (SECURE)", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4d21ae4f-79f7-460b-b584-d9cf013362b7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "7Cqv2KhmUIHqpaKr", "tags": []}