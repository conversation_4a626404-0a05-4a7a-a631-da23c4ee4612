{
  "name": "Telegram Bot Workflow - Final Fixed Version",
  "nodes": [
    {
      "parameters": {
        "updates": [
          "message",
          "callback_query",
          "chat_member",
          "pre_checkout_query",
          "successful_payment"
        ]
      },
      "id": "telegramTrigger",
      "name": "Telegram Trigger",
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.1,
      "position": [
        20,
        300
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM public.users WHERE telegram_id = '{{ $json.message?.from?.id || $json.callback_query?.from?.id || $json.chat_member?.from?.id || $json.pre_checkout_query?.from?.id || $json.successful_payment?.from?.id }}'",
        "options": {
          "alwaysOutputData": true
        }
      },
      "id": "lookupUser",
      "name": "1. Lookup User",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [
        240,
        300
      ],
      "credentials": {
        "postgres": {
          "id": "supabase_credentials",
          "name": "Supabase Connection"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "\n// --- Unified User State Handler ---\n// This node is the single source of truth for user context and routing.\n\n// Standardize input from various trigger types\nconst message = $json.message || $json.callback_query?.message;\nconst callbackData = $json.callback_query?.data;\nconst text = $json.message?.text;\nconst isNewMember = !!$json.chat_member;\n\n// Safely get user data from the 'Lookup User' node that ran before this one.\nconst userRecord = $items('Lookup User')[0]?.json;\n\n// --- 1. Determine User State & Role ---\nlet user = userRecord;\nlet userState = 'guest'; // Default state\n\nif (user) {\n  // User exists in our database\n  switch(user.role) {\n    case 'admin':\n    case 'superuser':\n      userState = 'admin';\n      break;\n    case 'investor':\n      userState = 'investor';\n      break;\n    case 'member':\n    default:\n      userState = 'member';\n      break;\n  }\n} else {\n  // User is unknown, create a consistent 'guest' object\n  const from = $json.message?.from || $json.callback_query?.from || $json.chat_member?.from || $json.pre_checkout_query?.from || $json.successful_payment?.from;\n  user = {\n    telegram_id: from.id,\n    username: from.username || '',\n    name: from.first_name || 'Guest',\n    role: 'guest',\n    is_verified_investor: false\n  };\n}\n\n// --- 2. Determine Interaction Type for Routing ---\nlet interactionType = 'unhandled'; // Default for fallback\n\nif (text?.startsWith('/start') || isNewMember) {\n  interactionType = 'welcome';\n} else if (callbackData) {\n  interactionType = 'callback';\n} else if (text?.startsWith('/')) {\n  interactionType = 'admin_command';\n} else if ($json.pre_checkout_query) {\n  interactionType = 'pre_checkout';\n} else if ($json.successful_payment) {\n  interactionType = 'successful_payment';\n} else {\n  interactionType = 'unhandled';\n}\n\n// --- 3. Return a clean, unified JSON object for the next node ---\nconst chatId = message?.chat?.id || $json.callback_query?.message?.chat?.id || $json.pre_checkout_query?.from?.id || $json.successful_payment?.from?.id;\n\nreturn {\n  ...$json, // Pass through original data if needed\n  user, // The full, normalized user object\n  userState, // 'guest', 'member', 'investor', 'admin'\n  interactionType, // 'welcome', 'callback', 'admin_command', 'pre_checkout', 'successful_payment', 'unhandled'\n  unifiedChatId: chatId, // Ensure a consistent chat ID\n  callbackData // Pass callback data forward\n};\n"
      },
      "id": "unifiedUserState",
      "name": "2. Unified User State Handler",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        460,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "\n// --- Smart Interaction Router ---\n// Directs the flow based on the clean state determined by the previous node.\n\nconst { interactionType } = $json;\n\nswitch(interactionType) {\n  case 'welcome':\n    return { output: 0 }; // Path for new users and /start command\n  \n  case 'callback':\n    return { output: 1 }; // Path for all inline button presses\n\n  case 'admin_command':\n    return { output: 2 }; // Path for admin commands like /kick, /pin\n\n  case 'pre_checkout':\n    return { output: 3 }; // Path for pre-checkout queries\n\n  case 'successful_payment':\n    return { output: 4 }; // Path for successful payments\n\n  case 'unhandled':\n  default:\n    return { output: 5 }; // Fallback path for anything else\n}\n"
      },
      "id": "smartInteractionRouter",
      "name": "3. Smart Interaction Router",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        680,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// --- Dynamic Menu Builder ---\n// Creates a context-aware menu based on the user's state.\n\nconst { user, userState, unifiedChatId } = $json;\n\n// Define all possible menus in one place for easy management\n// Using single quotes (') for JS strings to avoid JSON escaping issues.\nconst menus = {\n  guest: {\n    welcome: '👋 Welcome to ELOH Processing DAO\\n\\n🔓 You are browsing as a Guest.',\n    buttons: [\n      [{ text: '🗺️ View Roadmap', callback_data: 'show_roadmap_public' }],\n      [{ text: '💰 Investment Info', callback_data: 'guest_investment_info' }],\n      [{ text: '✅ Create Account', callback_data: 'register_user' }]\n    ]\n  },\n  member: {\n    welcome: '🚀 Welcome back, ' + (user.name || 'Member') + '!',\n    buttons: [\n      [{ text: '🗺️ Roadmap', callback_data: 'show_roadmap_public' }],\n      [{ text: '🔧 Our Services', callback_data: 'services_menu' }],\n      [{ text: '💝 Make a Donation', callback_data: 'donate' }]\n    ]\n  },\n  investor: {\n    welcome: '💎 Welcome, Investor ' + (user.name || 'User') + '!',\n    buttons: [\n      [{ text: '💰 My Investment', callback_data: 'view_investment' }],\n      [{ text: '🔧 Our Services', callback_data: 'services_menu' }],\n      [{ text: '🗺️ Roadmap', callback_data: 'show_roadmap_public' }]\n    ]\n  },\n  admin: {\n    welcome: '🛡️ Admin Panel - Welcome ' + (user.name || 'Admin'),\n    buttons: [\n      [{ text: '👥 Manage Users', callback_data: 'admin_users' }],\n      [{ text: '📊 View Analytics', callback_data: 'admin_analytics' }],\n      [{ text: '🔧 Our Services', callback_data: 'services_menu' }]\n    ]\n  }\n};\n\n// Select the correct menu for the current user, defaulting to guest\nconst currentMenu = menus[userState] || menus.guest;\n\n// This is the standard format for a Telegram Sender node\nreturn {\n  chatId: unifiedChatId,\n  text: currentMenu.welcome,\n  reply_markup: {\n    inline_keyboard: currentMenu.buttons\n  },\n  parse_mode: 'Markdown'\n};\n"
      },
      "id": "dynamicMenuBuilder",
      "name": "Dynamic Menu Builder",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        900,
        0
      ]
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "={{ $json.text }}",
        "replyMarkup": "={{ $json.reply_markup }}",
        "parseMode": "={{ $json.parse_mode }}"
      },
      "id": "telegramSender",
      "name": "Send Menu",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1120,
        0
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "value": "={{ $json.callbackData }}",
        "routing": {
          "options": {
            "caseSensitive": true,
            "fallback": "default",
            "regex": false,
            "typeValidation": "strict"
          },
          "rules": [
            {
              "value": "pay_",
              "output": 0,
              "mode": {
                "type": "string",
                "operation": "startsWith"
              }
            },
            {
              "value": "show_roadmap_public",
              "output": 1,
              "mode": {
                "type": "string",
                "operation": "equals"
              }
            },
            {
              "value": "show_next_milestone_public",
              "output": 2,
              "mode": {
                "type": "string",
                "operation": "equals"
              }
            }
          ]
        }
      },
      "id": "callbackHandler",
      "name": "Callback Handler",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 2,
      "position": [
        900,
        200
      ]
    },
    {
      "parameters": {
        "jsCode": "const d = $json.callbackData;\nconst usd = d.replace('pay_', '');\nreturn { json: { ...$json, usd, cents: parseInt(usd) * 100, uid: $json.user.telegram_id, ts: Date.now() } };"
      },
      "id": "processPaymentAmount",
      "name": "Process Payment Amount",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1120,
        100
      ]
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "title": "ELOH Deposit",
        "description": "Secure investment deposit",
        "payload": "DAO-DEPOSIT-{{ $json.uid }}-{{ $json.ts }}",
        "providerToken": "YOUR_PAYMENT_PROVIDER_TOKEN_FROM_BOTFATHER",
        "currency": "USD",
        "prices": {
          "pricesValues": [
            {
              "label": "Deposit Amount",
              "amount": "={{ $json.cents }}"
            }
          ]
        }
      },
      "id": "sendInvoice",
      "name": "Send Invoice",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1340,
        100
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM public.project_roadmap ORDER BY id"
      },
      "id": "getRoadmapData",
      "name": "Get Roadmap Data",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [
        1120,
        200
      ],
      "credentials": {
        "postgres": {
          "id": "supabase_credentials",
          "name": "Supabase Connection"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "const rows = $input.first().json;\nlet txt = '📣 **ELOH Processing Public Roadmap**\\n\\n';\nrows.forEach(r => txt += `- **${r.name}**: ${r.status}\\n`);\nreturn { json: { ...$input.first().json, formatted: txt } };"
      },
      "id": "formatRoadmapText",
      "name": "Format Roadmap Text",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1340,
        200
      ]
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "={{ $json.formatted }}"
      },
      "id": "showFullRoadmap",
      "name": "Show Full Roadmap",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1560,
        200
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT * FROM public.project_roadmap WHERE status IN ('Upcoming','In Progress') ORDER BY id LIMIT 1"
      },
      "id": "getNextMilestone",
      "name": "Get Next Milestone",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [
        1120,
        300
      ],
      "credentials": {
        "postgres": {
          "id": "supabase_credentials",
          "name": "Supabase Connection"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "🎯 **Next Milestone**: {{ $items('Get Next Milestone')[0].json.name }}\\n\\n*Details*: {{ $items('Get Next Milestone')[0].json.details }}"
      },
      "id": "showNextMilestone",
      "name": "Show Next Milestone",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1340,
        300
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "Admin command received: {{ $json.message.text }}"
      },
      "id": "adminCommandProcessor",
      "name": "Admin Command Processor",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        900,
        400
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "operation": "answerPreCheckoutQuery",
        "preCheckoutQueryId": "={{ $json.pre_checkout_query.id }}",
        "ok": true
      },
      "id": "answerPreCheckoutQuery",
      "name": "Answer Pre-Checkout Query",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        900,
        500
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO public.payments (user_id, amount, currency, created_at, telegram_charge_id) VALUES ($1, $2, $3, NOW(), $4)",
        "values": "={{ [$json.successful_payment.from.id, $json.successful_payment.total_amount / 100, $json.successful_payment.currency, $json.successful_payment.telegram_payment_charge_id] }}"
      },
      "id": "recordPayment",
      "name": "Record Payment (SECURE)",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2.4,
      "position": [
        900,
        600
      ],
      "credentials": {
        "postgres": {
          "id": "supabase_credentials",
          "name": "Supabase Connection"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "✅ Payment of ${{ $json.successful_payment.total_amount / 100 }} {{ $json.successful_payment.currency }} received! Thank you for your investment."
      },
      "id": "sendPaymentConfirmation",
      "name": "Send Payment Confirmation",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1120,
        600
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{ $json.unifiedChatId }}",
        "text": "Sorry, I don't understand that command."
      },
      "id": "unhandledMessage",
      "name": "Unhandled Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        900,
        700
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram_bot_credentials",
          "name": "Telegram Bot API"
        }
      }
    }
  ],
  "connections": {
    "telegramTrigger": [
      {
        "node": "lookupUser",
        "type": "main",
        "index": 0
      }
    ],
    "lookupUser": [
      {
        "node": "unifiedUserState",
        "type": "main",
        "index": 0
      }
    ],
    "unifiedUserState": [
      {
        "node": "smartInteractionRouter",
        "type": "main",
        "index": 0
      }
    ],
    "smartInteractionRouter": [
      [
        {
          "node": "dynamicMenuBuilder",
          "type": "main",
          "index": 0
        }
      ],
      [
        {
          "node": "callbackHandler",
          "type": "main",
          "index": 0
        }
      ],
      [
        {
          "node": "adminCommandProcessor",
          "type": "main",
          "index": 0
        }
      ],
      [
        {
          "node": "answerPreCheckoutQuery",
          "type": "main",
          "index": <strong>0</strong>
        }
      ],
      [
        {
          "node": "recordPayment",
          "type": "main",
          "index": 0
        }
      ],
      [
        {
          "node": "unhandledMessage",
          "type": "main",
          "index": 0
        }
      ]
    ],
    "dynamicMenuBuilder": [
      {
        "node": "telegramSender",
        "type": "main",
        "index": 0
      }
    ],
    "callbackHandler": [
      [
        {
          "node": "processPaymentAmount",
          "type": "main",
          "index": 0
        }
      ],
      [
        {
          "node": "getRoadmapData",
          "type": "main",
          "index": 0
        }
      ],
      [
        {
          "node": "getNextMilestone",
          "type": "main",
          "index": 0
        }
      ]
    ],
    "processPaymentAmount": [
      {
        "node": "sendInvoice",
        "type": "main",
        "index": 0
      }
    ],
    "getRoadmapData": [
      {
        "node": "formatRoadmapText",
        "type": "main",
        "index": 0
      }
    ],
    "formatRoadmapText": [
      {
        "node": "showFullRoadmap",
        "type": "main",
        "index": 0
      }
    ],
    "getNextMilestone": [
      {
        "node": "showNextMilestone",
        "type": "main",
        "index": 0
      }
    ],
    "recordPayment": [
      {
        "node": "sendPaymentConfirmation",
        "type": "main",
        "index": 0
      }
    ]
  }
}