{"name": "My workflow 3", "nodes": [{"parameters": {"updates": ["message", "chat_member", "callback_query", "pre_checkout_query"]}, "id": "4cfeff66-8115-44ae-bce4-80f34be17752", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-2720, -160], "webhookId": "76bfb192-83ad-4541-9197-36d47cf7049b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"values": {"string": [{"name": "botUsername", "value": "elohprocessingbot"}]}}, "id": "02d87d0d-b0a8-42f9-94ca-506867be25c1", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-2576, -160]}, {"parameters": {"jsCode": "const {message, callback_query: callbackQuery, chat_member: chatMember} = $json;\nconst getUser = (obj) => obj?.from || obj?.new_chat_member?.user;\nconst getChat = (obj) => obj?.chat || obj?.message?.chat;\n\nconst user = getUser(message) || getUser(callbackQuery) || getUser(chatMember);\nconst chat = getChat(message) || getChat(callbackQuery) || getChat(chatMember);\n\nreturn {\n  json: {\n    ...$json,\n    unifiedUserId: user?.id,\n    unifiedChatId: chat?.id,\n    unifiedUsername: user?.username,\n    unifiedFirstName: user?.first_name,\n    unifiedLastName: user?.last_name,\n    isCallback: !!callbackQuery,\n    isNewChatMember: !!chatMember\n  }\n};"}, "id": "737f9830-89db-4fab-b92f-3bfbe5313a19", "name": "Standardize User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2288, -160]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.pre_checkout_query }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ $json.message?.successful_payment }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}], "combineOperation": "any"}}, "id": "b0e34b97-f379-4cf6-a993-2e90babeb6d5", "name": "Event Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2144, -160]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.unifiedUserId }}", "rightValue": "", "operation": {"type": "string", "operation": "isNotEmpty", "rightType": "any"}}, {"leftValue": "={{ typeof $json.unifiedUserId }}", "rightValue": "undefined", "operation": {"type": "string", "operation": "notEqual", "rightType": "any"}}], "combineOperation": "all"}}, "id": "55488bba-5a24-4169-b5bb-7bab3b909e4b", "name": "Validate User ID", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2640, 32]}, {"parameters": {"jsCode": "return { json: $json };"}, "id": "6e2d2b59-fca0-46ab-8b01-a0085a31b36d", "name": "Debug Before User Lookup", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2432, 16]}, {"parameters": {"jsCode": "return $input.all();"}, "id": "63fff928-536d-4ffc-8487-38f43de83f3d", "name": "Test Supabase Connection", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2432, -160]}, {"parameters": {"operation": "getAll", "tableId": "users", "returnAll": true, "matchType": "allFilters", "filters": {"conditions": [{"keyName": "telegram_id", "condition": "eq", "keyValue": "={{ $json.unifiedUserId.toString() }}"}, {"keyName": "user_id", "condition": "is", "keyValue": "null"}]}}, "id": "1c7bf89f-daf6-4e9c-bf77-430c7da3c200", "name": "Check Existing User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2288, 16], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const originalData = $('Debug Before User Lookup').item.json;\nconst supabaseResults = $json;\nconst results = Array.isArray(supabaseResults) ? supabaseResults : [supabaseResults];\n\nreturn {\n  json: {\n    ...originalData,\n    supabaseResults: results,\n    length: results.length,\n    userFound: results.length > 0\n  }\n};"}, "id": "e9d43ae2-1424-46c9-b62f-c6a2ea1dfabf", "name": "Merge User Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2144, 32]}, {"parameters": {"tableId": "users", "dataToSend": "autoMapInputData"}, "id": "33de75e0-050f-41ce-a172-45a6b3ad2e00", "name": "Create New User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2608, 688], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "return {\n  json: {\n    ...$input.first().json,\n    user: $json[0] || $json,\n    is_new_user: true\n  }\n};"}, "id": "5f7f01ce-f688-4a49-a863-9acfb2fc830e", "name": "<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-528, 464]}, {"parameters": {"jsCode": "const originalData = $('Standardize User Data').item.json;\nreturn {\n  json: {\n    ...originalData,\n    user: $json[0] || $json,\n    is_new_user: false\n  }\n};"}, "id": "ab9f9817-3a3e-425b-bdaf-8c5a730acf3d", "name": "<PERSON> Existing User", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1776, -32]}, {"parameters": {"dataType": "string", "value1": "={{ $json.message ? ($json.message.text.includes(' ') ? $json.message.text.split(' ')[0] : $json.message.text) : ($json.callback_query ? 'callback' : ($json.chat_member ? 'new_member' : '')) }}", "rules": {"rules": [{"value2": "/start"}, {"value2": "show", "output": 1}, {"value2": "/kick", "output": 2}, {"value2": "/pin", "output": 2}, {"value2": "view", "output": 2}, {"value2": "new", "output": 3}, {"value2": "services", "output": 3}, {"value2": "donate", "output": 3}, {"value2": "order", "output": 3}, {"value2": "callback", "output": 3}]}}, "id": "ea826abe-e494-4c79-871c-1fb1acb8da96", "name": "Main Switch", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-2160, 464], "notes": "Routes all incoming events. Enhanced to handle payment callbacks."}, {"parameters": {"jsCode": "// Get user data and determine role\nlet user = $json.user;\nif (!user) {\n  const nodes = ['Guest Mode Handler', 'Mark Existing User', 'Mark New User'];\n  for (const nodeName of nodes) {\n    try {\n      const nodeData = $(nodeName)?.item?.json;\n      if (nodeData?.user) {\n        user = nodeData.user;\n        break;\n      }\n    } catch (e) {}\n  }\n}\nif (!user) user = { name: 'Guest', role: 'guest', is_verified_investor: false };\n\nconst chatId = $json.unifiedChatId || $json.chatId || $('Standardize User Data').item.json.unifiedChatId;\nconst role = user.role || 'guest';\nconst isNewUser = $json.is_new_user;\n\n// Role-based menu configurations\nconst menus = {\n  guest: {\n    title: '� *ELOH Processing DAO - Guest Area*',\n    subtitle: '� **Guest Access** - Register to unlock full features',\n    keyboard: [\n      [{ text: '🗺️ Public Roadmap', callback_data: 'show_roadmap_public' }],\n      [{ text: '📊 Mining Stats', callback_data: 'guest_mining_stats' }],\n      [{ text: '🔧 Browse Services', callback_data: 'services_menu' }],\n      [{ text: '💝 Donate', callback_data: 'donate' }],\n      [{ text: '✅ Create Account', callback_data: 'register_user' }]\n    ]\n  },\n  member: {\n    title: `� *Welcome ${user.name || 'Member'}!*`,\n    subtitle: isNewUser ? '✅ Account created successfully' : '� **Community Member**',\n    keyboard: [\n      [{ text: '🗺️ Roadmap', callback_data: 'show_roadmap_public' }],\n      [{ text: '🔧 Services', callback_data: 'services_menu' }],\n      [{ text: '💝 Donate', callback_data: 'donate' }],\n      [{ text: '📞 Support', url: 'https://elohprocessing.site/contact.php' }]\n    ]\n  },\n  investor: {\n    title: `� *Welcome ${user.name || 'Investor'}!*`,\n    subtitle: '� **Verified Investor Portal**',\n    keyboard: [\n      [{ text: '💰 My Investment', callback_data: 'view_investment' }],\n      [{ text: '📊 Portfolio', callback_data: 'investor_portfolio' }],\n      [{ text: '🗺️ Roadmap', callback_data: 'show_roadmap_public' }],\n      [{ text: '🔧 Services', callback_data: 'services_menu' }],\n      [{ text: '💝 Donate', callback_data: 'donate' }]\n    ]\n  }\n};\n\n// Determine user type\nlet userType = 'guest';\nif (role !== 'guest') {\n  userType = user.is_verified_investor ? 'investor' : 'member';\n}\n\nconst menu = menus[userType];\n\nreturn {\n  chatId,\n  text: `${menu.title}\\n\\n${menu.subtitle}\\n\\n*🏭 Sustainable Crypto Mining in Dominica*`,\n  reply_markup: { inline_keyboard: menu.keyboard },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query?.message?.message_id : undefined\n};"}, "id": "5392aa0f-47dc-411e-857a-e63bece99baa", "name": "Build Main Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1264, -16]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId }}", "text": "={{ $json.text.toString() }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "={{ $json.reply_markup.inline_keyboard[0][0].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[0][0].callback_data.toString() }}"}}, {"text": "={{ $json.reply_markup.inline_keyboard[0][1].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[0][1].callback_data }}"}}]}}, {"row": {"buttons": [{"text": "={{ $json.reply_markup.inline_keyboard[1][0].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[1][0].callback_data }}"}}, {"text": "={{ $json.reply_markup.inline_keyboard[1][1].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[1][1].callback_data }}"}}, {"text": "={{ $json.reply_markup.inline_keyboard[2][0].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[2][0].callback_data }}"}}, {"text": "={{ $json.reply_markup.inline_keyboard[2][1].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[2][1].callback_data }}"}}]}}, {"row": {"buttons": [{"text": "={{ $json.reply_markup.inline_keyboard[3][0].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[3][0].url }}"}}, {"text": "={{ $json.reply_markup.inline_keyboard[3][1].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[3][1].url }}"}}]}}, {"row": {"buttons": [{"text": "={{ $json.reply_markup.inline_keyboard[4][0].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[4][0].callback_data }}"}}, {"text": "={{ $json.reply_markup.inline_keyboard[4][1].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[4][1].callback_data }}"}}]}}, {"row": {"buttons": [{"text": "={{ $json.reply_markup.inline_keyboard[5][0].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[5][0].callback_data }}"}}]}}]}}, "id": "93b6ed5c-bc9b-42e5-9ca1-1daf341b795d", "name": "Send Main Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1072, 96], "webhookId": "f7808a34-8de2-4cca-9313-c1ec353740df", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "project_roadmap", "filters": {"conditions": []}}, "id": "a4f64187-884f-47d8-92ed-a92a39c0e6ac", "name": "Get Roadmap Data", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-912, 256], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nlet formattedText = '🗺️ **ELOH Processing Public Roadmap**\\n\\n';\nfor (const item of items) {\n  formattedText += `- **${item.json.name}**: ${item.json.status}\\n`;\n}\n\n$input.first().json.formatted_text = formattedText;\nreturn $input.first();"}, "id": "6130d624-fa64-4a94-afdf-65a4ed91bb3f", "name": "Format Roadmap Text", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-736, 256]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.formatted_text }}"}, "id": "87e6db7a-94d5-4f79-a4d1-5761a9bfacce", "name": "Send Roadmap", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-528, 256], "webhookId": "cf66780a-dbcf-441d-aa1b-e62ffe47170e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Welcome @{{ $json.unifiedUsername }} to ELOH Processing DAO! 🎉  Please review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}}, "id": "2f139963-bc61-4a70-bf2e-71f8bfc83418", "name": "Send Welcome", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1440, 112], "webhookId": "04b66bdf-bcbc-4144-b12f-6b25d86e4b0c", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id", "keyValue": "="}]}}, "id": "e08bbc4d-1e77-4fb2-b362-5aa620cd1b2b", "name": "Check User in Database", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1104, 448], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "=Hello {{ $json.user?.name || 'Investor' }}! Your current investment value is: ${{ $json.user?.investment_details?.total_value_usd || '0' }}."}, "id": "33f605eb-396e-43b7-83eb-86c06a6bfa84", "name": "Send Investment Details", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [384, 656], "webhookId": "3fd54c83-3909-4de6-a53c-34c37f734e3b", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Your Telegram account is not yet linked to a verified investor profile. Please contact support.", "replyMarkup": "forceReply", "forceReply": {}}, "id": "5529fbd2-3c74-4bcb-969d-c2c887f430d7", "name": "Send Not Verified Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [624, 1088], "webhookId": "e797834f-b962-4a70-ad7c-7d45762ad3aa", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const user = $json.user || { role: 'guest' };\nconst role = user.role || 'guest';\nconst chatId = $json.unifiedChatId;\nconst messageId = $json.isCallback ? $json.callback_query.message.message_id : undefined;\n\n// Role-based service menus\nconst serviceMenus = {\n  guest: {\n    title: '🔧 *ELOH Services - Guest View*',\n    subtitle: '🔓 **Information Only** - Register to purchase',\n    keyboard: [\n      [{ text: '🔍 Mining Operations - $500/mo', callback_data: 'guest_service_mining' }],\n      [{ text: '⛏️ Pool Membership - $200/yr', callback_data: 'guest_service_pool' }],\n      [{ text: '� Consulting - $150/hr', callback_data: 'guest_service_consulting' }],\n      [{ text: '✅ Register to Purchase', callback_data: 'register_user' }],\n      [{ text: '🏠 Main Menu', callback_data: 'main_menu' }]\n    ]\n  },\n  member: {\n    title: '🔧 *ELOH Services - Member Portal*',\n    subtitle: '💳 **Full Access** - Purchase available',\n    keyboard: [\n      [{ text: '💳 Mining Operations - $500/mo', callback_data: 'order_mining_500' }],\n      [{ text: '💳 Pool Membership - $200/yr', callback_data: 'order_pool_200' }],\n      [{ text: '💳 Consulting - $150/hr', callback_data: 'order_consulting_150' }],\n      [{ text: '💳 Analysis Report - $99', callback_data: 'order_analysis_99' }],\n      [{ text: '🏠 Main Menu', callback_data: 'main_menu' }]\n    ]\n  },\n  investor: {\n    title: '🔧 *ELOH Services - Investor Portal*',\n    subtitle: '💎 **Premium Access** - Investor rates available',\n    keyboard: [\n      [{ text: '� Mining Operations - $450/mo', callback_data: 'order_mining_450' }],\n      [{ text: '� Pool Membership - $150/yr', callback_data: 'order_pool_150' }],\n      [{ text: '� Priority Consulting - $120/hr', callback_data: 'order_consulting_120' }],\n      [{ text: '� Premium Analysis - $79', callback_data: 'order_analysis_79' }],\n      [{ text: '🏠 Main Menu', callback_data: 'main_menu' }]\n    ]\n  }\n};\n\n// Determine user type\nlet userType = 'guest';\nif (role !== 'guest') {\n  userType = user.is_verified_investor ? 'investor' : 'member';\n}\n\nconst menu = serviceMenus[userType];\nconst services = '\\n\\n• **Mining**: 24/7 sustainable ASIC operations\\n• **Pool**: Transparent mining pool membership\\n• **Consulting**: Expert crypto & forex guidance\\n• **Analysis**: Detailed market insights';\n\nreturn {\n  chatId,\n  text: `${menu.title}\\n\\n${menu.subtitle}${services}`,\n  reply_markup: { inline_keyboard: menu.keyboard },\n  parse_mode: 'Markdown',\n  messageId\n};"}, "id": "463b8fe7-07a7-405f-8e09-187b5bd6d6e4", "name": "Build Services Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1648, 880]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}"}, "id": "a8357697-07b7-4bb3-bdf1-cd81d6495db2", "name": "Send Services Menu", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1424, 880], "webhookId": "ce4c0427-72a9-451e-aa40-4b9dac6bcbf8", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "const keyboard = [\n  [{ text: \"$5 - Support ELOH\", callback_data: \"donate_5\" }],\n  [{ text: \"$10 - Fuel Operations\", callback_data: \"donate_10\" }],\n  [{ text: \"$25 - Expand Infrastructure\", callback_data: \"donate_25\" }],\n  [{ text: \"Custom Amount\", callback_data: \"donate_custom\" }],\n  [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n];\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💝 *Support ELOH Processing DAO*\\n\\nYour generous contributions help us maintain and expand our sustainable crypto mining operations in Dominica. Every donation, big or small, makes a difference!\\n\\nChoose a preset amount or enter a custom amount:`,\n  reply_markup: { inline_keyboard: keyboard },\n  parse_mode: 'Markdown',\n  messageId: $json.isCallback ? $json.callback_query.message.message_id : undefined\n};"}, "id": "c691daca-9afe-4ce4-ba6b-0c419d83d956", "name": "Build Donate Menu", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1776, 1440]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}"}, "id": "c99b0d5a-966e-47fe-a4c7-896d0450b9e1", "name": "Send Donate <PERSON>u", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1616, 1440], "webhookId": "1cca6b01-d29b-4f44-95fc-73fa22064cac", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "Sorry, I didn't understand that. Please use the menu buttons."}, "id": "68cd4283-0562-4291-a9b7-80e145f64893", "name": "Unhandled Input", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2320, 1040], "webhookId": "54a07ea0-e42b-49cd-8ad1-57700d90dc54", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "get", "tableId": "users", "filters": {"conditions": [{"keyName": "telegram_id"}, {"keyName": "role"}]}}, "id": "b21b403d-b567-4173-ab12-f12180ba5507", "name": "Check if User is Admin1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-2128, 1040], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"chatId": "={{ $json.message.chat.id }}", "text": "Admin command received. This feature is coming soon!"}, "id": "50d58b31-bf74-46f8-90f7-969bba2a7399", "name": "Admin Action Placeholder", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [208, 304], "webhookId": "5b9eb8ba-07ba-49b3-9f01-81b6def74252", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "=Hello {{ $json.user?.name || 'Investor' }}! Your current investment value is: ${{ $json.user?.investment_details?.total_value_usd || '0' }}."}, "id": "f3fc1c38-b721-4ce7-81f1-2ed1fcbe8020", "name": "Send Investment Details1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-64, 528], "webhookId": "2a151dcc-fc11-477a-accf-388dd945cf76", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "❌ *Invalid User Data*\n\nSorry, we couldn't process your request due to invalid user information. This might happen if:\n\n• Your user ID is missing or corrupted\n• There's a temporary data issue\n• The bot needs to be restarted\n\n🔄 Please try using /start to restart the bot.\n📞 If the problem persists, contact support."}, "id": "bf2c78c9-7719-49d7-a7f8-a56ce9964455", "name": "Invalid User E<PERSON><PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2560, 1024], "webhookId": "dcfd838e-0704-44c5-a6f5-3c50e6fac1bf", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.unifiedChatId || $json.message?.chat?.id || $json.callback_query?.message?.chat?.id }}", "text": "🔧 *Database Connection Error*\n\nWe're experiencing temporary technical difficulties with our database. Please try again in a few moments.\n\n⏳ This is usually resolved quickly\n🔄 Use /start to try again\n📞 Contact support if the issue persists"}, "id": "764ac976-d179-41c8-b7f3-24fa9aaf47cf", "name": "Database Error <PERSON>", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2528, 816], "webhookId": "7f250e6b-0761-43ec-b6a9-3aa81a7d9943", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a4b559b8-ff00-40c1-9af6-25320ae40940", "leftValue": "={{ $json.supabaseResults[0] }}", "rightValue": "", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1968, -48], "id": "ad7e60f9-7d42-46f3-9a54-f9ad0d0b5918", "name": "User Check"}, {"parameters": {"jsCode": "const originalData = $('Standardize User Data').item.json;\nconst guestUser = {\n  telegram_id: originalData.unifiedUserId,\n  username: originalData.unifiedUsername || '',\n  name: originalData.unifiedFirstName || 'Guest',\n  is_verified_investor: false,\n  role: 'guest',\n  is_guest: true,\n  created_at: new Date().toISOString()\n};\n\nreturn {\n  json: {\n    ...originalData,\n    user: guestUser,\n    is_new_user: false,\n    is_guest: true\n  }\n};"}, "id": "ab9c2630-08bd-4622-b53e-c5b0af5a9719", "name": "Guest <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1872, 176]}, {"parameters": {"jsCode": "const user = $json.user || { role: 'guest' };\nconst orderMatch = ($json.callback_query?.data || '').match(/order_(.+)_(\\d+)/);\nconst chatId = $json.unifiedChatId;\nconst messageId = $json.callback_query?.message?.message_id;\n\nif (!orderMatch) {\n  return {\n    chatId,\n    text: '❌ *Invalid Order*\\n\\nPlease select a service from the menu.',\n    reply_markup: { inline_keyboard: [[{ text: '🏠 Main Menu', callback_data: 'main_menu' }]] },\n    parse_mode: 'Markdown',\n    messageId\n  };\n}\n\nconst [, serviceName, price] = orderMatch;\nconst service = serviceName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n\n// Role-based order handling\nif (user.role === 'guest') {\n  return {\n    chatId,\n    text: `🔐 *Account Required*\\n\\n**${service}** - $${price}\\n\\nCreate an account to purchase services and unlock member benefits.`,\n    reply_markup: {\n      inline_keyboard: [\n        [{ text: '✅ Create Account', callback_data: 'register_user' }],\n        [{ text: '🏠 Main Menu', callback_data: 'main_menu' }]\n      ]\n    },\n    parse_mode: 'Markdown',\n    messageId\n  };\n}\n\n// Member/Investor order confirmation\nconst userType = user.is_verified_investor ? 'Investor' : 'Member';\nconst discount = user.is_verified_investor ? ' (Investor Rate)' : '';\n\nreturn {\n  chatId,\n  text: `🛒 *Order Confirmation*\\n\\n**Service:** ${service}\\n**Price:** $${price}${discount}\\n**Account:** ${userType}\\n\\nProceed with secure payment?`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: '💳 Pay Securely', callback_data: `pay_${serviceName}_${price}` }],\n      [{ text: '🔧 Back to Services', callback_data: 'services_menu' }],\n      [{ text: '🏠 Main Menu', callback_data: 'main_menu' }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId\n};"}, "id": "0aef3f3b-37e1-491b-94db-6cc16086f1d8", "name": "Order Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-784, 1344]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "additionalFields": {}}, "id": "823628bb-31e8-4947-a779-f7afcb8b3278", "name": "Send Order Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-576, 1344], "webhookId": "order-response-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"dataType": "string", "value1": "={{ $json.callback_query ? $json.callback_query.data : 'no_callback' }}", "rules": {"rules": [{"value2": "main_menu"}, {"value2": "services_menu", "output": 1}, {"value2": "donate", "output": 2}, {"value2": "register_user", "output": 3}, {"value2": "register_for_investment", "output": 4}, {"value2": "view_investment", "output": 5}, {"value2": "confirm_registration", "output": 6}, {"value2": "final_confirm_registration", "output": 7}, {"value2": "cancel_registration"}, {"value2": "donate_5", "output": 8}, {"value2": "donate_10", "output": 8}, {"value2": "donate_25", "output": 8}, {"value2": "donate_custom", "output": 8}, {"value2": "guest_mining_stats", "output": 9}, {"value2": "guest_investment_info", "output": 10}, {"value2": "guest_registration_info", "output": 11}, {"value2": "guest_continue", "output": 12}, {"value2": "guest_landing", "output": 12}, {"value2": "show_roadmap_public", "output": 13}, {"value2": "registration_info", "output": 11}, {"value2": "custom_confirm_registration", "output": 7}, {"value2": "show_terms", "output": 14}, {"value2": "show_privacy", "output": 15}, {"value2": "registration_settings", "output": 16}, {"value2": "registration_help", "output": 17}, {"value2": "save_registration", "output": 18}]}}, "id": "a5661760-f6dd-4131-894e-16913a5dc7bd", "name": "Callback Router", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-1712, 448]}, {"parameters": {"jsCode": "const user = $json.user || { role: 'guest' };\nconst chatId = $json.unifiedChatId || $json.chatId || $json.callback_query?.message?.chat?.id || $json.message?.chat?.id;\nconst messageId = $json.callback_query?.message?.message_id;\n\nif (!chatId) throw new Error('Chat ID required');\n\n// Check if already registered\nif (user.role !== 'guest') {\n  return {\n    chatId,\n    text: '✅ *Already Registered*\\n\\nYou have full access to all features.',\n    reply_markup: { inline_keyboard: [[{ text: '🏠 Main Menu', callback_data: 'main_menu' }]] },\n    parse_mode: 'Markdown',\n    messageId\n  };\n}\n\n// Guest registration prompt\nreturn {\n  chatId,\n  text: '📝 *Create ELOH Account*\\n\\n**Unlock Full Access:**\\n💰 Investment portal\\n🔧 Service purchases\\n� Portfolio tracking\\n\\n**Quick & Secure:**\\n✅ Uses your Telegram info\\n🔒 GDPR compliant\\n⚡ Instant activation',\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: '✅ Create Account', callback_data: 'confirm_registration' }],\n      [{ text: '❌ Stay Guest', callback_data: 'main_menu' }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId\n};"}, "id": "2874b2e0-d7cf-4515-8f0c-4b518abc62ef", "name": "Registration Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-416, 816]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.reply_markup.inline_keyboard[0][0].text }}", "replyMarkup": "={{ $json.reply_markup.inline_keyboard[0][0].callback_data }}", "additionalFields": {}}, "id": "9bc0d2af-2c4c-411f-8961-aa64c4eb4285", "name": "Send Registration Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-224, 816], "webhookId": "registration-response-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Get chat ID from multiple sources\nlet chatId = $json.unifiedChatId || $json.chatId || $json.callback_query?.message?.chat?.id || $json.message?.chat?.id;\nif (!chatId) {\n  try {\n    chatId = $('Standardize User Data').item.json.unifiedChatId;\n  } catch (e) {}\n}\n\n// Get user data\nlet userData = $json;\nif (!userData.unifiedFirstName) {\n  try {\n    userData = $('Standardize User Data').item.json;\n  } catch (e) {}\n}\n\nif (!chatId) throw new Error('Chat ID is required but not found');\n\nconst firstName = userData.unifiedFirstName || 'User';\nconst username = userData.unifiedUsername ? `@${userData.unifiedUsername}` : 'Not set';\n\nreturn {\n  chatId,\n  text: `🎯 *Welcome ${firstName}!*\\n\\n📋 **Registration Summary:**\\n👤 Name: ${firstName}\\n📱 Username: ${username}\\n🆔 Telegram ID: ${userData.unifiedUserId || 'Unknown'}\\n\\n🚀 **What You'll Get:**\\n💎 **Investment Portal** - Track your portfolio\\n📊 **Analytics Dashboard** - Real-time performance\\n🔧 **Premium Services** - Full access to all features\\n🔔 **Priority Support** - Direct line to our team\\n💰 **Dividend Tracking** - Automated profit sharing\\n📈 **Market Insights** - Exclusive research reports\\n\\n🔐 **Privacy & Security:**\\n• Your data is encrypted and secure\\n• GDPR compliant data handling\\n• You can delete your account anytime\\n\\n⚡ **Ready to unlock the full ELOH experience?**`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: `🎉 Yes, Create ${firstName}'s Account!`, callback_data: \"custom_confirm_registration\" }],\n      [{ text: \"📋 Review Terms & Conditions\", callback_data: \"show_terms\" }, { text: \"🔒 Privacy Policy\", callback_data: \"show_privacy\" }],\n      [{ text: \"⚙️ Customize Settings First\", callback_data: \"registration_settings\" }, { text: \"💬 Ask Questions\", callback_data: \"registration_help\" }],\n      [{ text: \"❌ Cancel Registration\", callback_data: \"guest_landing\" }, { text: \"⏸️ Save & Continue Later\", callback_data: \"save_registration\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "a204eeb3-a514-4162-9b77-d77c4a49713f", "name": "Registration Confirmation Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [256, 800]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "={{ $json.reply_markup.inline_keyboard[0][0].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[0][0].callback_data }}"}}, {"text": "={{ $json.reply_markup.inline_keyboard[1][0].text }}", "additionalFields": {"callback_data": "={{ $json.reply_markup.inline_keyboard[1][0].callback_data }}"}}]}}]}, "additionalFields": {}}, "id": "ad68997f-0062-4b06-ad3a-0834bd0b4533", "name": "Send Registration Confirmation", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [480, 912], "webhookId": "dce675ed-dbe9-432d-a1bb-ae03cf1accdb", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"operation": "insert"}, "id": "8cf59778-c62a-4e17-bb0a-911d37973656", "name": "Create Registered User", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-96, 1136], "credentials": {"supabaseApi": {"id": "JEUpDqrenfijzyI0", "name": "Supabase account"}}}, {"parameters": {"jsCode": "return {\n  chatId: $('Standardize User Data').item.json.unifiedChatId,\n  text: `🎉 *Registration Successful!*\\n\\nWelcome to ELOH Processing DAO, ${$json.name}!\\n\\n✅ Your account has been created\\n💎 You can now access investment features\\n🔧 Full service access unlocked\\n\\nUse /start to access the main menu with all features.`,\n  reply_markup: { inline_keyboard: [[{ text: \"🏠 Go to Main Menu\", callback_data: \"main_menu\" }]] },\n  parse_mode: 'Markdown'\n};"}, "id": "381b8f72-1462-4366-8b2f-1e25cf823888", "name": "Registration Success Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [96, 1136]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "={{ $json.reply_markup }}", "additionalFields": {}}]}}]}, "additionalFields": {}}, "id": "46ce7fb6-310c-4768-ada8-84d3139c4a1b", "name": "Send Registration Success", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [288, 1136], "webhookId": "registration-success-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Donation Processor - Handle donation amount selection\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\nconst callbackData = $json.callback_query?.data || '';\n\n// Extract donation amount\nlet amount = 0;\nif (callbackData.includes('donate_')) {\n  const amountMatch = callbackData.match(/donate_(\\d+|custom)/);\n  if (amountMatch) {\n    amount = amountMatch[1] === 'custom' ? 'custom' : parseInt(amountMatch[1]);\n  }\n}\n\nif (amount === 'custom') {\n  return {\n    chatId: $json.unifiedChatId,\n    text: \"💝 *Custom Donation Amount*\\n\\nPlease enter your desired donation amount in USD (minimum $1):\",\n    reply_markup: {\n      inline_keyboard: [[\n        { text: \"❌ Cancel\", callback_data: \"donate\" }\n      ]]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\nif (amount > 0) {\n  const guestMessage = isGuest ? '\\n\\n🔓 *Note:* You\\'re donating as a guest. Consider registering for donation tracking and tax receipts.' : '';\n  \n  return {\n    chatId: $json.unifiedChatId,\n    text: `💝 *Donation Confirmation*\\n\\n**Amount:** $${amount}\\n**Purpose:** Support ELOH Processing DAO\\n\\nYour contribution helps fund our sustainable crypto mining operations in Dominica.${guestMessage}\\n\\nProceed with payment?`,\n    reply_markup: {\n      inline_keyboard: [\n        [{ text: \"💳 Donate Now\", callback_data: `pay_donation_${amount}` }],\n        [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n      ]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\nreturn {\n  chatId: $json.unifiedChatId,\n  text: \"❌ Invalid donation amount. Please try again.\",\n  reply_markup: {\n    inline_keyboard: [[\n      { text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }\n    ]]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "69be9584-ff8b-4fa1-99c5-6079deed87e9", "name": "Donation Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1424, 1440]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}"}, "id": "7358d7c3-8495-4153-bcba-a577403ca7f5", "name": "Send Donation Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1232, 1440], "webhookId": "donation-response-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Investment Registration Prompt - Handle guest users trying to access investment features\nconst user = $json.user;\nconst isGuest = $json.is_guest || user?.role === 'guest';\n\nif (isGuest) {\n  return {\n    chatId: $json.unifiedChatId,\n    text: \"🔐 *Investment Portal Access*\\n\\nTo access investment features, you need to register an account first.\\n\\n**Investment Features Include:**\\n💎 Portfolio tracking\\n📊 Investment dashboard\\n📈 Performance analytics\\n🔔 Investment notifications\\n💰 Dividend tracking\\n\\n**Registration Benefits:**\\n✅ Free account creation\\n🔒 Secure data protection\\n📱 Full bot access\\n🎯 Personalized experience\\n\\nWould you like to register now?\",\n    reply_markup: {\n      inline_keyboard: [\n        [{ text: \"✅ Register for Investment Access\", callback_data: \"register_user\" }],\n        [{ text: \"ℹ️ Learn More About Registration\", callback_data: \"registration_info\" }],\n        [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n      ]\n    },\n    parse_mode: 'Markdown',\n    messageId: $json.callback_query?.message?.message_id\n  };\n}\n\n// For registered users, redirect to investment check\nreturn {\n  redirect: 'investment_check',\n  ...($json)\n};"}, "id": "3aba197c-c349-47c0-be89-524e56dba1cf", "name": "Investment Registration Prompt", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-272, 1600]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}"}, "id": "eba76446-c98f-4ac1-ad07-ff9c69b9f689", "name": "Send Investment Prompt", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-592, 1536], "webhookId": "investment-prompt-webhook", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Guest Mining Stats - Show public mining statistics\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `📊 *ELOH Processing - Live Mining Stats*\\n\\n🔓 **Public Statistics** (Guest View)\\n\\n⚡ **Current Operations:**\\n• Active Miners: 24 ASIC units\\n• Hash Rate: ~2.4 PH/s\\n• Power Consumption: 100% renewable\\n• Uptime: 99.2%\\n\\n🌍 **Sustainability:**\\n• Location: Dominica (Caribbean)\\n• Energy: 100% geothermal & hydroelectric\\n• Carbon Footprint: Net negative\\n• Environmental Impact: Minimal\\n\\n💰 **Performance (Last 30 Days):**\\n• Blocks Mined: 12\\n• Revenue Generated: $18,500\\n• Operating Efficiency: 94%\\n• Pool Contribution: 2.1%\\n\\n🔐 *Register for detailed analytics and investment opportunities*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"📈 View Full Analytics (Register)\", callback_data: \"register_for_investment\" }],\n      [{ text: \"💰 Investment Opportunities\", callback_data: \"guest_investment_info\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "36ac16c9-8e8c-4c04-9e4b-ab66e6f22b65", "name": "Guest Mining Stats", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1360, 1136]}, {"parameters": {"jsCode": "// Guest Investment Info - Explain investment opportunities\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💎 *Why Invest with ELOH Processing?*\\n\\n🔓 **Guest Information**\\n\\n🏭 **Our Mission:**\\nSustainable cryptocurrency mining in the Caribbean using 100% renewable energy sources.\\n\\n💰 **Investment Highlights:**\\n• Minimum Investment: $1,000\\n• Expected ROI: 15-25% annually\\n• Dividend Payments: Monthly\\n• Transparency: Full operational visibility\\n• Sustainability: Carbon-negative operations\\n\\n🌟 **Investor Benefits:**\\n• Priority access to new mining capacity\\n• Quarterly performance reports\\n• Voting rights on major decisions\\n• Tax-optimized structure\\n• Liquidity options available\\n\\n📊 **Track Record:**\\n• 3+ years of profitable operations\\n• 99%+ uptime reliability\\n• Consistent dividend payments\\n• Growing mining capacity\\n\\n🔐 *Register now to access detailed investment materials and start your journey*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ Register to Invest\", callback_data: \"register_user\" }],\n      [{ text: \"📊 View Mining Stats\", callback_data: \"guest_mining_stats\" }],\n      [{ text: \"📞 Contact Investment Team\", url: \"https://elohprocessing.site/contact.php\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "66cefe63-363d-42db-93cb-ac122bb0ca67", "name": "Guest Investment Info", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1776, 1600]}, {"parameters": {"jsCode": "// Guest Registration Info - Explain registration benefits\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `ℹ️ *About ELOH Processing Registration*\\n\\n🔓 **Currently Browsing as Guest**\\n\\n✅ **Registration Benefits:**\\n\\n🔐 **Investment Access:**\\n• View detailed portfolio analytics\\n• Track investment performance\\n• Receive dividend notifications\\n• Access investor-only reports\\n\\n🎯 **Personalized Experience:**\\n• Customized dashboard\\n• Personal investment advisor\\n• Priority customer support\\n• Early access to new opportunities\\n\\n🔒 **Security & Privacy:**\\n• Secure account protection\\n• Encrypted data storage\\n• Two-factor authentication\\n• GDPR compliant\\n\\n💼 **Professional Features:**\\n• Tax reporting assistance\\n• Investment history tracking\\n• Performance benchmarking\\n• Portfolio optimization tools\\n\\n🆓 **Registration is completely FREE and takes less than 30 seconds!**\\n\\n*We only use your Telegram information - no additional data required.*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ Register Now (Free)\", callback_data: \"register_user\" }],\n      [{ text: \"💰 Learn About Investing\", callback_data: \"guest_investment_info\" }],\n      [{ text: \"🔄 Continue as Guest\", callback_data: \"guest_continue\" }],\n      [{ text: \"🏠 Back to Main Menu\", callback_data: \"main_menu\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "3c83abc5-8f4a-48c9-a711-eb1c4b0b3f47", "name": "Guest Registration Info", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1776, 1136]}, {"parameters": {"jsCode": "// Guest Landing Handler - Dedicated landing page for guests who cancel registration\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🏠 *ELOH Processing - Guest Dashboard*\n\n🔓 **Welcome Back, Guest User!**\n\nYou've chosen to continue without registration. No problem! You still have access to many features.\n\n✅ **What You Can Do:**\n• 🗺️ View our public roadmap\n• 📊 Check live mining statistics\n• 🔧 Browse our services (view-only)\n• 💝 Make donations to support us\n• 📞 Contact our support team\n• 🏭 Visit our operations center\n\n🔐 **Want More Access?**\nRegistration unlocks investment features, service purchases, and personalized dashboards.\n\n*Ready to explore what ELOH Processing has to offer?*`,\n  reply_markup: {\n    inline_keyboard: [\n      [\n        { text: \"🗺️ View Public Roadmap\", callback_data: \"show_roadmap_public\" },\n        { text: \"📊 Live Mining Stats\", callback_data: \"guest_mining_stats\" }\n      ],\n      [\n        { text: \"🔧 Browse Services\", callback_data: \"services_menu\" },\n        { text: \"💝 Make Donation\", callback_data: \"donate\" }\n      ],\n      [\n        { text: \"💰 Investment Info\", callback_data: \"guest_investment_info\" },\n        { text: \"ℹ️ About Registration\", callback_data: \"guest_registration_info\" }\n      ],\n      [\n        { text: \"🏭 Operations Center\", url: \"https://elohprocessing.site/operations.php\" },\n        { text: \"📞 Contact Support\", url: \"https://elohprocessing.site/contact.php\" }\n      ],\n      [\n        { text: \"✅ Register Account\", callback_data: \"register_user\" }\n      ]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "0945b10b-f4e3-48fb-9b57-629266853c06", "name": "Guest Continue <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1776, 1280]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}"}, "id": "2f79f7fd-858c-4dc7-98e8-97573cf839c1", "name": "Send Guest Mining Stats", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1136, 1136], "webhookId": "1b64d296-7171-4eb8-95a2-764e6cd60e8a", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}"}, "id": "337454de-0071-4424-9833-0f3cced57945", "name": "Send Guest Investment Info", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1472, 1600], "webhookId": "c8415b92-21ab-46e1-b865-17406f35c89e", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}"}, "id": "8052ed57-1f6d-4390-824e-7bc7a91b0ac5", "name": "Send Guest Registration Info", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1552, 1136], "webhookId": "d8c2f51d-72d0-4222-8a14-7eb4b5dcdd57", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"jsCode": "// Terms & Conditions Handler\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `📋 *ELOH Processing DAO - Terms & Conditions*\\n\\n**Service Agreement:**\\n\\n🔹 **Account Usage**\\n• One account per person\\n• Accurate information required\\n• Responsible use of platform\\n\\n🔹 **Investment Terms**\\n• Minimum investment: $1,000\\n• Returns not guaranteed\\n• Risk disclosure provided\\n\\n🔹 **Service Access**\\n• Mining services subject to availability\\n• Pool membership renewable annually\\n• Consulting by appointment only\\n\\n🔹 **Data & Privacy**\\n• We protect your personal information\\n• Data used for service delivery only\\n• You can request data deletion\\n\\n🔹 **Liability**\\n• Services provided 'as is'\\n• Limited liability for losses\\n• Dispute resolution via arbitration\\n\\n📄 *Full terms available at: elohprocessing.site/terms*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ I Accept Terms\", callback_data: \"accept_terms\" }],\n      [{ text: \"📖 Read Full Terms\", url: \"https://elohprocessing.site/terms.php\" }],\n      [{ text: \"🔙 Back to Registration\", callback_data: \"confirm_registration\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "terms-handler", "name": "Terms Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 1200]}, {"parameters": {"jsCode": "// Privacy Policy Handler\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `🔒 *ELOH Processing DAO - Privacy Policy*\\n\\n**Data Protection Summary:**\\n\\n🔹 **Information We Collect**\\n• Telegram ID and username\\n• Investment preferences\\n• Transaction history\\n• Communication logs\\n\\n🔹 **How We Use Data**\\n• Provide personalized services\\n• Process investments and payments\\n• Send important notifications\\n• Improve our platform\\n\\n🔹 **Data Security**\\n• End-to-end encryption\\n• Secure database storage\\n• Regular security audits\\n• GDPR compliance\\n\\n🔹 **Your Rights**\\n• Access your data anytime\\n• Request data correction\\n• Delete your account\\n• Opt-out of communications\\n\\n🔹 **Data Sharing**\\n• Never sold to third parties\\n• Only shared with consent\\n• Required legal disclosures only\\n\\n🛡️ *Full policy: elohprocessing.site/privacy*`,\n  reply_markup: {\n    inline_keyboard: [\n      [{ text: \"✅ I Accept Privacy Policy\", callback_data: \"accept_privacy\" }],\n      [{ text: \"📖 Read Full Policy\", url: \"https://elohprocessing.site/privacy.php\" }],\n      [{ text: \"🔙 Back to Registration\", callback_data: \"confirm_registration\" }]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "privacy-handler", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [600, 1200]}, {"parameters": {"jsCode": "// Registration Settings Handler\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `⚙️ *Registration Settings*\\n\\n**Customize Your Experience:**\\n\\n🔔 **Notifications**\\n• Investment updates: ON\\n• Market alerts: ON\\n• Service announcements: ON\\n• Weekly reports: ON\\n\\n📊 **Dashboard Preferences**\\n• Default view: Portfolio\\n• Currency display: USD\\n• Time zone: Auto-detect\\n• Chart style: Candlestick\\n\\n🎯 **Investment Profile**\\n• Risk tolerance: Moderate\\n• Investment focus: Mining\\n• Preferred communication: Telegram\\n• Language: English\\n\\n💡 **Tips:**\\n• Settings can be changed anytime\\n• Notifications help track performance\\n• Custom dashboards coming soon!`,\n  reply_markup: {\n    inline_keyboard: [\n      [\n        { text: \"🔔 Notification Settings\", callback_data: \"notification_settings\" },\n        { text: \"📊 Dashboard Settings\", callback_data: \"dashboard_settings\" }\n      ],\n      [\n        { text: \"🎯 Investment Profile\", callback_data: \"investment_profile\" },\n        { text: \"🌐 Language & Region\", callback_data: \"language_settings\" }\n      ],\n      [\n        { text: \"✅ Save & Continue Registration\", callback_data: \"custom_confirm_registration\" }\n      ],\n      [\n        { text: \"🔙 Back to Registration\", callback_data: \"confirm_registration\" }\n      ]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "settings-handler", "name": "Registration Settings Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 1200]}, {"parameters": {"jsCode": "// Registration Help Handler\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `💬 *Registration Help & FAQ*\\n\\n**Common Questions:**\\n\\n❓ **Is registration really free?**\\n✅ Yes! Completely free, no hidden fees.\\n\\n❓ **What information do you need?**\\n✅ Just your Telegram info - that's it!\\n\\n❓ **Can I delete my account later?**\\n✅ Absolutely, anytime you want.\\n\\n❓ **How secure is my data?**\\n✅ Bank-level encryption, GDPR compliant.\\n\\n❓ **What's the minimum investment?**\\n✅ $1,000 for investment features.\\n\\n❓ **Do I need to invest immediately?**\\n✅ No! Browse first, invest when ready.\\n\\n❓ **Can I change my settings later?**\\n✅ Yes, full control over your preferences.\\n\\n❓ **How do I contact support?**\\n✅ Direct message us anytime!\\n\\n🤝 **Still have questions?**\\nOur team is here to help!`,\n  reply_markup: {\n    inline_keyboard: [\n      [\n        { text: \"💬 Chat with Support\", url: \"https://t.me/elohprocessing\" },\n        { text: \"📧 Email Support\", url: \"mailto:<EMAIL>\" }\n      ],\n      [\n        { text: \"📞 Schedule Call\", callback_data: \"schedule_call\" },\n        { text: \"📚 Knowledge Base\", url: \"https://elohprocessing.site/help\" }\n      ],\n      [\n        { text: \"✅ Questions Answered, Let's Register!\", callback_data: \"custom_confirm_registration\" }\n      ],\n      [\n        { text: \"🔙 Back to Registration\", callback_data: \"confirm_registration\" }\n      ]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "help-handler", "name": "Registration Help Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 1200]}, {"parameters": {"jsCode": "// Save Registration Handler\nreturn {\n  chatId: $json.unifiedChatId,\n  text: `⏸️ *Registration Saved*\\n\\n**Your Progress:**\\n✅ Account details collected\\n✅ Preferences noted\\n⏸️ Registration paused\\n\\n**What Happens Next:**\\n• Your information is temporarily saved\\n• You can continue anytime within 7 days\\n• No account created yet (you're still a guest)\\n• All guest features remain available\\n\\n**To Complete Registration:**\\n• Use /start and click \\\"Complete Registration\\\"\\n• Or click \\\"Register Account\\\" from any menu\\n• Your saved preferences will be restored\\n\\n**Why Save for Later?**\\n• Take time to read our materials\\n• Discuss with family/advisors\\n• Research our investment opportunities\\n• No pressure, register when ready!\\n\\n💡 *Tip: Bookmark our website for easy access to resources*`,\n  reply_markup: {\n    inline_keyboard: [\n      [\n        { text: \"📚 Browse Resources\", url: \"https://elohprocessing.site/resources\" },\n        { text: \"📊 View Public Stats\", callback_data: \"guest_mining_stats\" }\n      ],\n      [\n        { text: \"💰 Learn About Investing\", callback_data: \"guest_investment_info\" },\n        { text: \"🗺️ View Roadmap\", callback_data: \"show_roadmap_public\" }\n      ],\n      [\n        { text: \"✅ Actually, Let's Complete Now!\", callback_data: \"custom_confirm_registration\" }\n      ],\n      [\n        { text: \"🏠 Guest Dashboard\", callback_data: \"guest_landing\" }\n      ]\n    ]\n  },\n  parse_mode: 'Markdown',\n  messageId: $json.callback_query?.message?.message_id\n};"}, "id": "save-registration-handler", "name": "Save Registration Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1200, 1200]}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId || $('Standardize User Data').item.json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}"}, "id": "0d9c87fc-0508-4f2a-9f1f-86c52f0cb01b", "name": "Send Guest Continue", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1536, 1280], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b2dd2aca-bca3-4d2e-bab1-8eb1c29cc228", "leftValue": "={{ $('Merge User Data').item.json.supabaseResults[0].role }}", "rightValue": "={{ $('Merge User Data').item.json.supabaseResults[0].role }}", "operator": {"type": "string", "operation": "equals"}}, {"id": "68e03373-512c-4b91-a1e3-8658a4a7e05c", "leftValue": "={{ $('Merge User Data').item.json.supabaseResults[0].role }}", "rightValue": "\"\"", "operator": {"type": "string", "operation": "empty", "singleValue": true}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-896, 448], "id": "9535eb34-3817-4193-8c71-cf01618b8235", "name": "If role"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4f9d987e-e837-46a6-9497-77e3f47592da", "leftValue": "={{ $json.role }}", "rightValue": "admin", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "ed6d0ee3-4494-4724-a89f-91073ebef6d2", "leftValue": "={{ $json.role }}", "rightValue": "superuser", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-736, 448], "id": "c6eeed31-103f-4fb3-b964-379c1201c733", "name": "If ADMIN"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4f9d987e-e837-46a6-9497-77e3f47592da", "leftValue": "={{ $json.role }}", "rightValue": "user", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-720, 640], "id": "32c1c29e-ac5f-4101-999a-5f3cebb70fc8", "name": "If GUEST"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4f9d987e-e837-46a6-9497-77e3f47592da", "leftValue": "={{ $json.role }}", "rightValue": "member", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-896, 608], "id": "ea98738b-d1e8-4a23-bf8f-c2496f41df6f", "name": "If MEMBER"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4f9d987e-e837-46a6-9497-77e3f47592da", "leftValue": "={{ $json.role }}", "rightValue": "investor", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1088, 576], "id": "9aaa2c52-8507-4cbd-9675-14e9dbf88fdc", "name": "If INVESTOR"}, {"parameters": {"jsCode": "// Create guest user for browsing\nconst originalData = $('Standardize User Data').item.json;\n\nconst guestUser = {\n  telegram_id: originalData.unifiedUserId,\n  username: originalData.unifiedUsername || '',\n  name: originalData.unifiedFirstName || 'Guest',\n  role: 'guest',\n  is_verified_investor: false\n};\n\nreturn {\n  json: {\n    ...originalData,\n    user: guestUser,\n    is_new_user: false,\n    is_guest: true\n  }\n};"}, "id": "01f9ce00-4c70-4deb-af1c-d762bdf1b91d", "name": "Guest Mode Handler1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-432, 640]}, {"parameters": {"chatId": "={{ $json.unifiedChatId }}", "text": "=Welcome @{{ $json.unifiedUsername }} to ELOH Processing DAO! 🎉\n\nPlease review our rules and start a private chat for investor features.", "replyMarkup": "inlineKeyboard", "inlineKeyboard": {"rows": [{"row": {"buttons": [{"text": "💬 Start Private Chat", "additionalFields": {"url": "={{ 'https://t.me/' + $node['Set Variables'].json.botUsername }}"}}]}}]}, "additionalFields": {}}, "id": "cfedd105-b0e9-41d2-9e1d-deae05330f2a", "name": "Send Welcome1", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-448, 1008], "webhookId": "04b66bdf-bcbc-4144-b12f-6b25d86e4b0c", "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"id": "92bf4d15-dfe7-4427-9ae7-7970fd40a672", "leftValue": "={{ $json.result.reply_markup.inline_keyboard[0][0].callback_data }}{{ $('Registration Handler').item.json.reply_markup.inline_keyboard[0][0].callback_data }}", "rightValue": "=confirm_registration", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "looseTypeValidation": true, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [16, 816], "id": "b88fa897-92c0-4c7c-a7ed-d0a18ac42f28", "name": "If"}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "parseMode": "={{ $json.parse_mode }}", "messageId": "={{ $json.messageId }}", "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "send-terms", "name": "Send Terms", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [600, 1400], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "parseMode": "={{ $json.parse_mode }}", "messageId": "={{ $json.messageId }}", "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "send-privacy", "name": "Send Privacy", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [800, 1400], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "parseMode": "={{ $json.parse_mode }}", "messageId": "={{ $json.messageId }}", "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "send-settings", "name": "Send Registration Settings", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1000, 1400], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "parseMode": "={{ $json.parse_mode }}", "messageId": "={{ $json.messageId }}", "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "send-help", "name": "Send Registration Help", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1200, 1400], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}, {"parameters": {"chatId": "={{ $json.chatId || $json.unifiedChatId }}", "text": "={{ $json.text }}", "replyMarkup": "={{ $json.reply_markup }}", "parseMode": "={{ $json.parse_mode }}", "messageId": "={{ $json.messageId }}", "replyKeyboardOptions": {}, "replyKeyboardRemove": {}, "additionalFields": {}}, "id": "send-save-registration", "name": "Send Save Registration", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1400, 1400], "credentials": {"telegramApi": {"id": "S3mx1dNYh7twonoL", "name": "Telegram account 2"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Test Supabase Connection", "type": "main", "index": 0}]]}, "Standardize User Data": {"main": [[{"node": "Event Router", "type": "main", "index": 0}]]}, "Event Router": {"main": [[{"node": "Validate User ID", "type": "main", "index": 0}]]}, "Create New User": {"main": [[{"node": "Database Error <PERSON>", "type": "main", "index": 0}]]}, "Mark New User": {"main": [[{"node": "Guest Mode Handler1", "type": "main", "index": 0}]]}, "Main Switch": {"main": [[{"node": "Callback Router", "type": "main", "index": 0}, {"node": "Send Welcome", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Check User in Database", "type": "main", "index": 0}], [{"node": "Callback Router", "type": "main", "index": 0}]]}, "Send Welcome": {"main": [[{"node": "Build Main Menu", "type": "main", "index": 0}]]}, "Build Main Menu": {"main": [[{"node": "Send Main Menu", "type": "main", "index": 0}]]}, "Mark Existing User": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Get Roadmap Data": {"main": [[{"node": "Format Roadmap Text", "type": "main", "index": 0}]]}, "Format Roadmap Text": {"main": [[{"node": "Send Roadmap", "type": "main", "index": 0}]]}, "Build Services Menu": {"main": [[{"node": "Send Services Menu", "type": "main", "index": 0}]]}, "Build Donate Menu": {"main": [[{"node": "Send Donate <PERSON>u", "type": "main", "index": 0}]]}, "Check User in Database": {"main": [[{"node": "If role", "type": "main", "index": 0}]]}, "Check Existing User": {"main": [[{"node": "Merge User Data", "type": "main", "index": 0}]]}, "Merge User Data": {"main": [[{"node": "User Check", "type": "main", "index": 0}]]}, "Test Supabase Connection": {"main": [[{"node": "Standardize User Data", "type": "main", "index": 0}]]}, "Validate User ID": {"main": [[{"node": "Debug Before User Lookup", "type": "main", "index": 0}]]}, "Debug Before User Lookup": {"main": [[{"node": "Check Existing User", "type": "main", "index": 0}]]}, "User Check": {"main": [[{"node": "<PERSON> Existing User", "type": "main", "index": 0}], [{"node": "Guest <PERSON>", "type": "main", "index": 0}]]}, "Guest Mode Handler": {"main": [[{"node": "Main Switch", "type": "main", "index": 0}]]}, "Order Handler": {"main": [[{"node": "Send Order Response", "type": "main", "index": 0}]]}, "Callback Router": {"main": [[{"node": "Guest Mode Handler1", "type": "main", "index": 0}, {"node": "Check User in Database", "type": "main", "index": 0}], [{"node": "Build Services Menu", "type": "main", "index": 0}], [{"node": "Build Donate Menu", "type": "main", "index": 0}], [{"node": "Registration Handler", "type": "main", "index": 0}], [{"node": "Investment Registration Prompt", "type": "main", "index": 0}], [{"node": "Send Investment Details", "type": "main", "index": 0}], [{"node": "Registration Confirmation Handler", "type": "main", "index": 0}], [{"node": "Create Registered User", "type": "main", "index": 0}], [{"node": "Donation Processor", "type": "main", "index": 0}], [{"node": "Guest Mining Stats", "type": "main", "index": 0}], [{"node": "Guest Investment Info", "type": "main", "index": 0}], [{"node": "Guest Registration Info", "type": "main", "index": 0}], [{"node": "Guest Continue <PERSON>", "type": "main", "index": 0}], [{"node": "Get Roadmap Data", "type": "main", "index": 0}], [{"node": "Terms Handler", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Registration Settings Handler", "type": "main", "index": 0}], [{"node": "Registration Help Handler", "type": "main", "index": 0}], [{"node": "Save Registration Handler", "type": "main", "index": 0}]]}, "Registration Confirmation Handler": {"main": [[{"node": "Send Registration Confirmation", "type": "main", "index": 0}]]}, "Registration Handler": {"main": [[{"node": "Send Registration Response", "type": "main", "index": 0}]]}, "Create Registered User": {"main": [[{"node": "Registration Success Handler", "type": "main", "index": 0}]]}, "Registration Success Handler": {"main": [[{"node": "Send Registration Success", "type": "main", "index": 0}]]}, "Donation Processor": {"main": [[{"node": "Send Donation Response", "type": "main", "index": 0}]]}, "Investment Registration Prompt": {"main": [[{"node": "Send Investment Prompt", "type": "main", "index": 0}]]}, "Guest Mining Stats": {"main": [[{"node": "Send Guest Mining Stats", "type": "main", "index": 0}]]}, "Guest Investment Info": {"main": [[{"node": "Send Guest Investment Info", "type": "main", "index": 0}]]}, "Guest Registration Info": {"main": [[{"node": "Send Guest Registration Info", "type": "main", "index": 0}]]}, "Guest Continue Handler": {"main": [[{"node": "Send Guest Continue", "type": "main", "index": 0}]]}, "If role": {"main": [[{"node": "If ADMIN", "type": "main", "index": 0}], [{"node": "If INVESTOR", "type": "main", "index": 0}]]}, "If ADMIN": {"main": [[{"node": "Admin Action Placeholder", "type": "main", "index": 0}], [{"node": "If INVESTOR", "type": "main", "index": 0}]]}, "If MEMBER": {"main": [[{"node": "Callback Router", "type": "main", "index": 0}], [{"node": "If GUEST", "type": "main", "index": 0}]]}, "If INVESTOR": {"main": [[{"node": "Send Investment Details1", "type": "main", "index": 0}], [{"node": "If MEMBER", "type": "main", "index": 0}]]}, "Send Investment Details1": {"main": [[{"node": "Send Investment Details", "type": "main", "index": 0}]]}, "If GUEST": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Guest Mode Handler1": {"main": [[{"node": "Registration Handler", "type": "main", "index": 0}]]}, "Send Registration Response": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Send Donate Menu": {"main": []}, "Send Donation Response": {"main": [[{"node": "Order Handler", "type": "main", "index": 0}]]}, "Send Investment Prompt": {"main": [[{"node": "Order Handler", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Registration Confirmation Handler", "type": "main", "index": 0}], [{"node": "Send Not Verified Message", "type": "main", "index": 0}]]}, "Terms Handler": {"main": [[{"node": "Send Terms", "type": "main", "index": 0}]]}, "Privacy Handler": {"main": [[{"node": "Send Privacy", "type": "main", "index": 0}]]}, "Registration Settings Handler": {"main": [[{"node": "Send Registration Settings", "type": "main", "index": 0}]]}, "Registration Help Handler": {"main": [[{"node": "Send Registration Help", "type": "main", "index": 0}]]}, "Save Registration Handler": {"main": [[{"node": "Send Save Registration", "type": "main", "index": 0}]]}, "Send Terms": {"main": []}, "Send Privacy": {"main": []}, "Send Registration Settings": {"main": []}, "Send Registration Help": {"main": []}, "Send Save Registration": {"main": []}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a89e47b0-49d9-4e58-9845-ce7273fc85c9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "1f52f14222153875e5cd754160d07a91bc57a3dd7ee7266d4266eae0b531fc56"}, "id": "cl97drsgNofoPdxU", "tags": [{"createdAt": "2025-08-06T12:34:09.665Z", "updatedAt": "2025-08-06T12:34:09.665Z", "id": "EKr6mdBmEMIyCP37", "name": "ELOH"}]}